import React from 'react';

interface SubtitleTextProps {
  text?: string;
}

const subtitleStyles = {
  color: 'rgb(0, 0, 0)',
  fontFamily: 'Rubik',
  fontSize: '16px',
  fontWeight: 400,
  lineHeight: '24px',
  letterSpacing: '0%',
  textAlign: 'left' as const,
  margin: '0 0 16px 0'
};

const SubtitleText: React.FC<SubtitleTextProps> = ({ text = 'What will you be using Surely for?' }) => (
  <p style={subtitleStyles}>{text}</p>
);

export default SubtitleText;
