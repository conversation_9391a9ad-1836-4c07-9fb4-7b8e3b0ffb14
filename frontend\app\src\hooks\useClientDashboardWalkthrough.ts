import { useState, useEffect } from 'react';
import { useAuthContext } from '../context/AuthContext';

// Import images in reverse order as requested
import img1 from '../assets/images/walkthrough/Dashboard_walkthrough_FirstTimeUsers-2.png';
import img2 from '../assets/images/walkthrough/Dashboard_walkthrough_FirstTimeUsers-1.png';
import img3 from '../assets/images/walkthrough/Dashboard_walkthrough_FirstTimeUsers-3.png';
import img4 from '../assets/images/walkthrough/Dashboard_walkthrough_FirstTimeUsers.png';

const STORAGE_KEY = 'clientDashboardWalkthroughSeen';

export interface UseClientDashboardWalkthrough {
  shouldShow: boolean;
  markSeen: () => void;
  images: string[];
}

export function useClientDashboardWalkthrough(): UseClientDashboardWalkthrough {
  const [shouldShow, setShouldShow] = useState(false);
  const { user } = useAuthContext();
  
  // Define images array in reverse order as requested
  const images = [img1, img2, img3, img4];

  // Check localStorage and verification status on mount and when user changes
  useEffect(() => {
    try {
      if (typeof window !== 'undefined') {
        // If user is verified, never show walkthrough regardless of localStorage
        if (user?.profile?.email_verified) {
          setShouldShow(false);
          return;
        }
        
        // Only show if user is not verified AND hasn't seen it before
        const seen = localStorage.getItem(STORAGE_KEY);
        setShouldShow(!seen);
      }
    } catch (error) {
      console.warn('Failed to read walkthrough localStorage:', error);
      setShouldShow(false);
    }
  }, [user?.profile?.email_verified]);

  const markSeen = () => {
    try {
      if (typeof window !== 'undefined') {
        localStorage.setItem(STORAGE_KEY, 'true');
      }
      setShouldShow(false);
    } catch (error) {
      console.warn('Failed to set walkthrough localStorage:', error);
      setShouldShow(false);
    }
  };

  return {
    shouldShow,
    markSeen,
    images
  };
}

export default useClientDashboardWalkthrough;
