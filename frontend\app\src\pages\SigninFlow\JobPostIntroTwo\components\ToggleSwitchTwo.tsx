import React from 'react';
import { labelStyles } from '../../JobPostIntro/styles';

interface ToggleSwitchTwoProps {
  label?: string;
  isChecked: boolean;
  onChange: (checked: boolean) => void;
  description?: string;
}

const ToggleSwitchTwo: React.FC<ToggleSwitchTwoProps> = ({
  label,
  isChecked,
  onChange,
  description
}) => {
  const containerStyles: React.CSSProperties = {
    marginBottom: '14px',
  };

  const toggleContainerStyles: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    marginTop: '8px',
  };

  const toggleStyles: React.CSSProperties = {
    position: 'relative',
    width: '50px',
    height: '24px',
    backgroundColor: isChecked ? '#007bff' : '#ccc',
    borderRadius: '12px',
    transition: 'background-color 0.3s',
    cursor: 'pointer',
  };

  const toggleKnobStyles: React.CSSProperties = {
    position: 'absolute',
    top: '2px',
    left: isChecked ? '26px' : '2px',
    width: '20px',
    height: '20px',
    backgroundColor: 'white',
    borderRadius: '50%',
    transition: 'left 0.3s',
  };

  const descriptionStyles: React.CSSProperties = {
    fontSize: '14px',
    color: '#666',
    marginTop: '4px',
  };

  const labelTextStyles: React.CSSProperties = {
    marginLeft: '12px',
    fontSize: '16px',
  };

  const handleToggleClick = () => {
    onChange(!isChecked);
  };

  // If no label is provided, render as inline toggle
  if (!label) {
    return (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <div style={toggleStyles} onClick={handleToggleClick}>
          <div style={toggleKnobStyles} />
        </div>
        <span style={labelTextStyles}>{isChecked ? 'Yes' : 'No'}</span>
      </div>
    );
  }

  // Render with full label structure
  return (
    <div style={containerStyles}>
      <label style={labelStyles}>{label}</label>
      <div style={toggleContainerStyles}>
        <div style={toggleStyles} onClick={handleToggleClick}>
          <div style={toggleKnobStyles} />
        </div>
        <span style={labelTextStyles}>{isChecked ? 'Yes' : 'No'}</span>
      </div>
      {description && <p style={descriptionStyles}>{description}</p>}
    </div>
  );
};

export default ToggleSwitchTwo;
