// Styles for JobPostIntro components
export const containerStyles = {
  display: 'flex',
  flexDirection: 'column' as const,
  width: '1440px',
  minHeight: '100vh',
  backgroundColor: 'rgba(255, 255, 255, 1)',
  fontFamily: 'Rubik',
  position: 'relative' as const,
  margin: '0 auto'
};

// Header styles
export const headerStyles = {
  padding: '20px 60px',
};

// Main layout styles
export const mainLayoutStyles = {
    display: 'flex',
    flex: '1 0 auto'
  };

// Left panel styles
export const leftColumnStyles = {
  display: 'flex',
  flexDirection: 'column' as const,
  alignItems: 'flex-start',
  justifyContent: 'flex-start',
  padding: '80px 110px 40px 110px', // reduced top padding
  flex: 1
};

export const brandLogoStyles = {
  height: '24px'
};

export const centerContentStyles = {
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'flex-start',
  textAlign: 'left'
};

export const logoStyles = {
  width: '120px',
  marginBottom: '20px',
  marginTop: '40px'
};

export const titleHeaderStyles = {
  fontFamily: 'Rubik One',
  fontWeight: 600,
  fontStyle: 'normal',
  fontSize: '48px',
  lineHeight: '33px',
  letterSpacing: '0%',
  verticalAlign: 'bottom',
  color: '#111827',
  margin: '0 0 16px 0'
};

export const subHeadingStyles = {
  fontFamily: 'Rubik',
  fontSize: '16px',
  fontWeight: 400,
  fontStyle: 'normal',
  lineHeight: '100%',
  letterSpacing: '0%',
  color: '#374151',
  margin: '0 0 32px 0',
  whiteSpace: 'nowrap',
  overflow: 'visible',
  width: '100%',
  display: 'block'
};

export const stepperContainerStyles = {
  display: 'flex',
  gap: '12px'
};

export const activeStepStyles = {
  width: '44px',
  height: '44px',
  borderRadius: '50%',
  backgroundColor: '#3B82F6',
  border: '2px solid #3B82F6'
};

export const inactiveStepStyles = {
  width: '44px',
  height: '44px',
  borderRadius: '50%',
  border: '2px solid #D1D5DB'
};

// Right panel styles
export const rightColumnStyles = {
  display: 'flex',
  flexDirection: 'column' as const,
  justifyContent: 'flex-start',
  alignItems: 'center',
  padding: '0px 60px', // reduced padding
  borderLeft: '1px solid #E5E7EB',
  flex: 1
};

export const formContainerStyles = {
  width: '100%',
  maxWidth: '600px',
  display: 'flex',
  flexDirection: 'column'
};

export const formHeadingStyles = {
  fontFamily: 'Inter, sans-serif',
  fontSize: '24px',
  fontWeight: 600,
  color: '#111827',
  margin: '0 0 40px 0'
};

export const formGroupStyles = {
  marginBottom: '24px'
};

export const labelStyles = {
  fontFamily: 'Rubik',
  fontWeight: 500,
  fontStyle: 'Medium',
  fontSize: '20px',
  lineHeight: '100%',
  letterSpacing: '0%',
  color: '#374151',
  marginBottom: '8px',
  display: 'block'
};

// Base input styles
export const inputStyles = {
  width: '100%',
  height: '56px',
  borderRadius: '9999px',
  border: '1px solid #D1D5DB',
  background: 'white',
  padding: '0 24px',
  fontFamily: 'Inter, sans-serif',
  fontSize: '16px',
  color: '#111827',
  outline: 'none',
  boxSizing: 'border-box',
};

// Placeholder styles for inputs
export const placeholderStyles = {
  '&::placeholder': {
    fontFamily: 'Rubik',
    fontWeight: 300,
    fontStyle: 'normal',
    fontSize: '20px',
    lineHeight: '100%',
    letterSpacing: '0%',
    opacity: 1
  }
};

export const iconInputStyles = {
  ...inputStyles,
  ...placeholderStyles,
  padding: '0 24px 0 60px'
};

export const selectContainerStyles = {
  position: 'relative'
};

export const caretIconStyles = {
  position: 'absolute',
  right: '24px',
  top: '50%',
  transform: 'translateY(-50%)',
  pointerEvents: 'none',
  zIndex: 1,
  width: '16px',
  height: '16px'
};

export const iconStyles = {
  position: 'absolute',
  left: '24px',
  top: '50%',
  transform: 'translateY(-50%)',
  zIndex: 1,
  width: '24px',
  height: '24px'
};

export const operativesDisplayStyles = {
  width: '56px',
  height: '56px',
  borderRadius: '50%',
  border: '1px solid #D1D5DB',
  background: 'white',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  fontFamily: 'Inter, sans-serif',
  fontSize: '16px',
  color: '#111827',
  marginRight: '16px'
};

export const operativesButtonStyles = {
  width: '56px',
  height: '56px',
  borderRadius: '50%',
  border: 'none',
  background: 'transparent',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  cursor: 'pointer',
  padding: 0
};

export const rateInputStyles = {
  width: '140px',
  height: '56px',
  borderRadius: '9999px',
  border: '1px solid #D1D5DB',
  background: 'white',
  padding: '0 24px',
  fontFamily: 'Inter, sans-serif',
  fontSize: '16px',
  color: '#111827',
  textAlign: 'center',
  outline: 'none',
  boxSizing: 'border-box'
};

// Footer styles
export const footerStyles = {
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: '20px 60px'
};

export const actionContainerStyles = {
  display: 'flex',
  flexDirection: 'column' as const,
  alignItems: 'center',
  gap: '8px'
};

export const primaryButtonStyles = {
  width: '120px',
  height: '56px',
  borderRadius: '9999px',
  background: '#111827',
  color: '#FFFFFF',
  fontFamily: 'Inter, sans-serif',
  fontSize: '16px',
  fontWeight: 500,
  border: 'none',
  cursor: 'pointer'
};

export const secondaryButtonStyles = {
    width: 180,
    height: 65,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 1)',
    color: 'white',
    fontFamily: 'Rubik',
    fontSize: '16px',
    fontWeight: 500,
    lineHeight: '100%',
    letterSpacing: '0%',
    border: 'none',
    marginTop: '-27px',
    cursor: 'pointer',
    '&:hover': {
      opacity: 0.9
    }
  };

export const finalStepTextStyles = {
  fontFamily: 'Rubik',
  fontSize: '14px',
  fontWeight: 500,
  lineHeight: '100%',
  letterSpacing: '0%',
  color: 'rgba(54, 158, 205, 1)',
  marginTop: '4px',
  textAlign: 'center'
};
