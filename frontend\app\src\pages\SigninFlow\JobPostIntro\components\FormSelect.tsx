import React, { CSSProperties } from 'react';
import { labelStyles, inputStyles, selectContainerStyles, caretIconStyles } from '../styles';

interface Option {
  value: string;
  label: string;
}

interface FormSelectProps {
  label: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  options: Option[];
  placeholder?: string;
  caretIcon: string;
}

const FormSelect: React.FC<FormSelectProps> = ({ 
  label, 
  value, 
  onChange, 
  options, 
  placeholder = 'Select an option', 
  caretIcon 
}) => {
  return (
    <div style={{ marginBottom: '24px' } as CSSProperties}>
      <label style={labelStyles as CSSProperties}>
        {label}
      </label>
      <div style={selectContainerStyles as CSSProperties}>
        <img 
          src={caretIcon} 
          alt="Select" 
          style={caretIconStyles as CSSProperties} 
        />
        <select
          value={value}
          onChange={onChange}
          style={{
            ...inputStyles,
            appearance: 'none',
            WebkitAppearance: 'none',
            MozAppearance: 'none'
          } as CSSProperties}
        >
          <option value="" disabled>{placeholder}</option>
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
};

export default FormSelect;
