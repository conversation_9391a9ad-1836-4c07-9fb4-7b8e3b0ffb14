import React, { CSSProperties } from 'react';
import { stepperContainerStyles } from '../../JobPostIntro/styles';
import EllipseSvg from '../../../../assets/images/job-post-intro/Ellipse 1.svg';

interface StepperDotsTwoProps {
  totalSteps?: number;
}

const StepperDotsTwo: React.FC<StepperDotsTwoProps> = ({ totalSteps = 2 }) => {
  const stepperStyles = {
    ...stepperContainerStyles,
    alignItems: 'center'
  };

  const svgContainerStyles = {
    position: 'relative' as const,
    display: 'flex',
    alignItems: 'center',
    marginTop: '-10px',
    justifyContent: 'center'
  };

  const activeFillStyles = {
    position: 'absolute' as const,
    width: '33px',
    height: '33px',
    borderRadius: '50%',
    backgroundColor: 'rgba(112, 206, 229, 1)',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)'
  };

  return (
    <div style={stepperStyles as CSSProperties}>
      {Array.from({ length: totalSteps }, (_, index) => (
        <div key={index} style={svgContainerStyles}>
          <img src={EllipseSvg} alt="Step indicator" />
          <div style={activeFillStyles} />
        </div>
      ))}
    </div>
  );
};

export default StepperDotsTwo;
