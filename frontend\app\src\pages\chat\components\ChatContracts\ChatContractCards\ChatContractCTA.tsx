// @ts-nocheck
import { useRef, useEffect, useState } from 'react';
import { useAuthContext } from 'src/context/AuthContext';
import { useChatContext } from 'src/context/ChatContext';

import { deleteChat } from 'src/services/chat';
import {
  payEscrow,
  acceptContract,
  rejectContract,
  cancelContract,
  confirmShiftsContract,
  payOutstanding,
  confirmContract,
  getContractInvoice,
} from 'src/services/contracts';

import {
  ClientAskForSupportButton,
  ClientCancelContractButton,
  ClientPayNowButton,
  ClientPostReviewButton,
  ReportOperativeButton,
  ClientGetInvoiceButton,
} from './ChatContractButtons/ClientContractButtons';

import {
  OperatorAskForSupportButton,
  OperatorCancelContractButton,
  OperatorConfirmContractButton,
  OperatorPostReviewButton,
  OperatorConfirmContractTermsButton,
} from './ChatContractButtons/OperatorContractButtons';
import DeleteChatButton from './ChatContractButtons/DeleteChatButton';
import { contractStatuses, paymentStatuses, shiftStatuses, escrowStatuses } from '../contractStatues';
import ReviewShiftHours from './ChatContractButtons/ReviewShiftHoursButton';
import { Image, useToast } from 'reshaped';
import surelyIcon from '../../../../../assets/icons/surleyicon/surleyicon.png';
import { useNavigate } from 'react-router-dom';

const ChatContractCTA = ({ contract, chat, operator, handleCtaButtonsNumber }) => {
  const navigate = useNavigate();
  const { user, isClient } = useAuthContext();
  const { loadContract, handleGetChats, handleGetMessages, setLoadingContracts } = useChatContext();
  const buttonContainerRef = useRef(null);
  const toast = useToast();
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (buttonContainerRef.current) {
      const lastChildElements = Array.from(buttonContainerRef.current.children)?.length;
      handleCtaButtonsNumber(lastChildElements);
    }
  }, [contract, chat]);

  const contractId = contract?.id;
  const contractStatus = contract?.status;
  const escrowStatus = contract?.escrow_status;
  const escrowRate = contract?.escrow_rate;
  const shiftStatus = contract?.shifts_status;
  const paymentStatus = contract?.payment_status;
  const chatId = chat?.id;
  const nameShown = user?.profile?.id === chat?.sender_id ? chat?.receiver_name : chat?.sender_name;

  // Client show button checks
  const clientShowCancelButton =
    (shiftStatus === 'pending' || shiftStatus === 'confirmed' || shiftStatus === 'accepted') &&
    paymentStatus === 'pending' &&
    (escrowStatus === 'pending' || escrowStatus === 'paid') &&
    (contractStatus === 'Pending' || contractStatus === 'In progress');

  const clientShowEscrowButton =
    shiftStatus === 'confirmed' && paymentStatus === 'pending' && escrowStatus === 'pending' && contractStatus === 'Pending';

  const clientShowAskForSupportButton =
    (shiftStatus === 'accepted' || shiftStatus === 'confirmed' || shiftStatus === 'need_review' || shiftStatus === 'completed') &&
    (escrowRate === '100' ? paymentStatus === 'paid' : paymentStatus === 'pending') &&
    escrowStatus === 'paid' &&
    (contractStatus === 'Pending' || contractStatus === 'In progress' || contractStatus === 'Completed');

  const clientShowReviewShiftsButton =
    shiftStatus === 'need_review' && paymentStatus === 'pending' && escrowStatus === 'paid' && contractStatus === 'Completed';

  const clientShowPayButton = shiftStatus === 'completed' && paymentStatus === 'pending' && escrowStatus === 'paid' && contractStatus === 'Completed';

  const clientShowPostReviewButton =
    (shiftStatus === 'completed' || shiftStatus === 'need_review') &&
    paymentStatus === 'paid' &&
    escrowStatus === 'paid' &&
    contractStatus === 'Completed';

  const clientShowInvoiceButton =
    shiftStatus === 'completed' ||
    (shiftStatus === 'accepted' &&
      (escrowRate === '100' ? paymentStatus === 'paid' : paymentStatus === 'pending') &&
      escrowStatus === 'paid' &&
      contractStatus === 'Completed') ||
    contractStatus === 'In progress';

    // Operator show button checks
  const operatorShowRefuseButton =
    (shiftStatus === 'pending' || shiftStatus === 'confirmed') &&
    paymentStatus === 'pending' &&
    (escrowStatus === 'pending' || escrowStatus === 'paid') &&
    contractStatus === 'Pending';

  const operatorShowAcceptButton =
    shiftStatus === 'pending' && paymentStatus === 'pending' && escrowStatus === 'pending' && contractStatus === 'Pending';

  const operatorShowConfirmButton =
    shiftStatus === 'confirmed' &&
    (escrowRate === '100' ? paymentStatus === 'paid' : paymentStatus === 'pending') &&
    escrowStatus === 'paid' &&
    contractStatus === 'Pending';

  const operatorShowCancelButton =
    shiftStatus === 'accepted' && paymentStatus === 'pending' && escrowStatus === 'paid' && contractStatus === 'In progress';

  const operatorShowReviewShiftsButton =
    shiftStatus === 'need_review' && paymentStatus === 'pending' && escrowStatus === 'paid' && contractStatus === 'Completed';

  // Operator show button checks
  const operatorShowAskForSupportButton =
    (shiftStatus === 'accepted' || shiftStatus === 'confirmed' || shiftStatus === 'need_review' || shiftStatus === 'completed') &&
    (escrowRate === '100' ? paymentStatus === 'paid' : paymentStatus === 'pending') &&
    escrowStatus === 'paid' &&
    (contractStatus === 'Pending' || contractStatus === 'In progress' || contractStatus === 'Completed');

  const operatorShowPostReviewButton =
    (escrowRate === '100' ? shiftStatus === 'need_review' : shiftStatus === 'completed') &&
    paymentStatus === 'paid' &&
    escrowStatus === 'paid' &&
    contractStatus === 'Completed';

  const handleDeleteChat = async () => {
    const response = await deleteChat(chatId);
    return response;
  };

  const handleActionSuccess = async () => {
    try {
      if (setLoadingContracts) {
        setLoadingContracts(true);
      }
      
      if (!contractId || !chatId) {
        console.warn('Missing contractId or chatId for action success');
        return;
      }
      
      await Promise.all([
        loadContract(contractId),
        handleGetChats(),
        handleGetMessages()
      ]);
    } catch (error) {
      console.error('Error in handleActionSuccess:', error);
    } finally {
      if (setLoadingContracts) {
        setLoadingContracts(false);
      }
    }
  };

  const handleConfirmContractTerms = async () => {
    const response = await confirmContract(chatId, contractId);
    if (response) {
      await handleActionSuccess();
    }
    return response;
  };

  const handlePayEscrow = async (cardId: number, paymentMethodId: string) => {
    const payment = await payEscrow(cardId, paymentMethodId);
    if (payment) {
      await handleActionSuccess();
    }
    return payment;
  };

  const handleAcceptContract = async () => {
    setIsLoading(true);
    try {
      const accept = await acceptContract(chatId, contractId);
      if (accept) {
        await handleActionSuccess();
      }
      return accept;
    } finally {
      setIsLoading(false);
    }
  };

  const handleRejectContract = async () => {
    const reject = await rejectContract(chatId, contractId);
    if (reject) {
      await handleActionSuccess();
    }
    return reject;
  };

  const handleCancelContract = async (cancelReason: string) => {
    const cancel = await cancelContract(chatId, contractId, cancelReason);
    if (cancel) {
      await handleActionSuccess();
    }
    if (cancel?.error) {
      if (cancel?.message?.includes?.('Refund')) {
        toast.show({
          title: 'Cancelled successfully!',
          text: 'The contract was cancelled and the paid amount was refunded!',
          startSlot: <Image key='error' src={surelyIcon} className='h-[30px] w-[30px]' />,
        });
      }
    }
    return cancel;
  };

  const handleConfirmShiftsContract = async () => {
    const confirmShifts = await confirmShiftsContract(chatId, contractId);
    return confirmShifts;
  };

  const handlePayOutstanding = async (cardId: number, paymentMethodId: string) => {
    const outstandingPayment = await payOutstanding(cardId, paymentMethodId);
    return outstandingPayment;
  };

  if (isClient) {
    return (
      <div ref={buttonContainerRef} className={'flow-col flex h-auto max-h-[160px] flex-col gap-4 px-3 py-6 lg:px-6'}>
        {clientShowEscrowButton && <ClientPayNowButton payEscrow={handlePayEscrow} payOutstanding={handlePayOutstanding} contract={contract} />}
        {clientShowPayButton && <ClientPayNowButton payEscrow={handlePayEscrow} payOutstanding={handlePayOutstanding} contract={contract} />}
        {clientShowReviewShiftsButton && <ReviewShiftHours handleConfirmShiftsContract={handleConfirmShiftsContract} />}
        {clientShowPostReviewButton && <ClientPostReviewButton chat={chat} />}
        {clientShowAskForSupportButton && (
          <div className='flex w-full gap-4'>
            <ClientAskForSupportButton operator={operator} chat={chat} />
            <ReportOperativeButton operator={operator} chat={chat} />
          </div>
        )}
        {clientShowInvoiceButton && <ClientGetInvoiceButton contractId={contractId} contract={contract} />}
        {clientShowCancelButton && <ClientCancelContractButton contractId={contractId} handleCancelContract={handleCancelContract} />}
      </div>
    );
  }

  if (isClient === false) {
    return (
      <div ref={buttonContainerRef} className={'flow-col flex h-auto max-h-[160px] flex-col gap-3 p-6'}>
        {operatorShowAcceptButton && <OperatorConfirmContractTermsButton handleConfirmContractTerms={handleConfirmContractTerms} />}
        {operatorShowConfirmButton && (
          <OperatorConfirmContractButton 
            handleAcceptContract={handleAcceptContract} 
            contract={contract}
            isLoading={isLoading}
          />
        )}
        {/* {operatorShowReviewShiftsButton && <ReviewShiftHours handleConfirmShiftsContract={handleConfirmShiftsContract} />} */}
        {operatorShowPostReviewButton && <OperatorPostReviewButton chat={chat} />}
        {operatorShowAskForSupportButton && (
          <div className='flex w-full gap-4'>
            <OperatorAskForSupportButton chat={chat} />
            <ReportOperativeButton operator={operator} chat={chat} />
          </div>
        )}
        {operatorShowCancelButton && <OperatorCancelContractButton refuseContract={handleCancelContract} contract={contract} />}
        {operatorShowRefuseButton && <OperatorCancelContractButton refuseContract={handleRejectContract} contract={contract} />}
      </div>
    );
  }
};

export default ChatContractCTA;
