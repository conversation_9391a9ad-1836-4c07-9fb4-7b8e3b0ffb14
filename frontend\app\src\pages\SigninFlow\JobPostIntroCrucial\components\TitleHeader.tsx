import React from 'react';

interface TitleHeaderProps {
  text?: string;
}

const titleStyles = {
  color: 'rgb(0, 0, 0)',
  fontFamily: 'Rubik One',
  fontSize: '48px',
  fontWeight: 600,
  lineHeight: '33px',
  letterSpacing: '0%',
  textAlign: 'left' as const,
  margin: '0 0 16px 0'
};

const TitleHeader: React.FC<TitleHeaderProps> = ({ text = "Select one of the options" }) => (
  <h1 style={titleStyles}>{text}</h1>
);

export default TitleHeader;
