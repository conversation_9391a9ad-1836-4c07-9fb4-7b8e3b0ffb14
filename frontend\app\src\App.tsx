// @ts-nocheck
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import { Reshaped } from 'reshaped';
import './themes/productTheme/theme.css';
import Echo from 'laravel-echo';
import Router from './router';
import { AppProvider } from './context/AppContext';
import { AuthProvider, useAuthContext } from './context/AuthContext';
import { ModalProvider } from './context/ModalContext';
import ManagedModal from './components/modal';
import { ToastProvider } from './context/ToastSystemContext';

import QueryProvider from './client/query-provider';
import { RegistrationProvider } from './context/RegistrationContext';
import JobProvider from './context/JobContext';
import OperativesProvider from './context/OperativesContext';
import { ChatProvider, useChatContext, useChatMessageDispatch } from './context/ChatContext';

import Pusher from 'pusher-js';

// Initialize Pusher properly
const pusher = new Pusher(import.meta.env.VITE_REACT_APP_MIX_PUSHER_APP_KEY, {
  cluster: import.meta.env.VITE_REACT_APP_MIX_PUSHER_APP_CLUSTER,
  encrypted: true
});

// Initialize Echo with the Pusher instance
window.Echo = new Echo({
  broadcaster: 'pusher',
  key: import.meta.env.VITE_REACT_APP_MIX_PUSHER_APP_KEY,
  cluster: import.meta.env.VITE_REACT_APP_MIX_PUSHER_APP_CLUSTER,
  encrypted: true
});

const App = () => {
  return (
    <HelmetProvider>
      <RegistrationProvider>
        <AuthProvider>
          <AppProvider>
            <JobProvider>
              <ModalProvider>
                <OperativesProvider>
                  <QueryProvider>
                    <Reshaped theme='productTheme'>
                      <BrowserRouter>
                        <ToastProvider>
                          <ChatProvider>
                            <div className='App'>
                              <Router />
                              <ManagedModal />
                            </div>
                          </ChatProvider>
                        </ToastProvider>
                      </BrowserRouter>
                    </Reshaped>
                  </QueryProvider>
                </OperativesProvider>
              </ModalProvider>
            </JobProvider>
          </AppProvider>
        </AuthProvider>
      </RegistrationProvider>
    </HelmetProvider>
  );
};

export default App;
