// @ts-nocheck
import React, { useState, useContext } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useToast, Image } from 'reshaped';
import { useRegistrationContext } from 'src/context/RegistrationContext';
import { registerOperator } from 'src/services/user';
import { AuthContext } from 'src/context/AuthContext';
import { AppContext } from 'src/context/AppContext';
import surleyicon from '../../../assets/icons/surleyicon/surleyicon.png';

// Import only the specified components
import {
  PrimaryButton,
  LogoIcon,
  HeaderLogo
} from '../JobPostIntro/components';

// Import new option button components and custom text components
import {
  HireOptionButton,
  JobOptionButton,
  TitleHeader,
  SubtitleText
} from './components';

// Import styles from JobPostIntro
import {
  containerStyles,
  headerStyles,
  mainLayoutStyles,
  leftColumnStyles,
  rightColumnStyles,
  centerContentStyles,
  logoStyles,
  footerStyles,
  actionContainerStyles,
  finalStepTextStyles
} from '../JobPostIntro/styles';

// Import assets
import surelyLogo from '../../../assets/images/job-post-intro/surely-logo.png';

const JobPostIntroCrucial: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const toast = useToast();
  const { sharedRegisterData } = useRegistrationContext();
  const { authenticateUser } = useContext(AuthContext);
  const { fetchAppData } = useContext(AppContext);
  const [selectedType, setSelectedType] = useState<string | null>(location.state?.userType || null);
  const [token] = useState(location.state?.referralToken ?? null);

  const handleContinue = async () => {
    if (!selectedType) {
      toast.show({
        title: 'Choose one of these options:',
        text: 'Are you a Security Operative or a Client?',
        startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
      });
      return;
    }

    try {
      const account_type = selectedType === 'client' ? 2 : 1;
      const registerFinalData = {
        ...sharedRegisterData.baseData,
        accountType: account_type,
        phone: '**********', // Default phone to pass validation
        city: 'Not Specified', // Default city to pass validation
        postCode: '00000', // Default postcode to pass validation
        ref: 'register',
        referFriend: token && token.trim() ? token : null,
      };

      // Register the user with the selected type
      let response = await registerOperator(registerFinalData);
      if (response.token) {
        authenticateUser(response);
      }
      fetchAppData();
      
      // Navigate based on user type
      if (selectedType === 'client') {
        navigate('/job-post-intro');
        toast.show({
          title: 'Account Created Successfully',
          text: 'Welcome to Surely! Now let\'s set up your job posting.',
          startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
        });
      } else {
        navigate('/my-profile');
        toast.show({
          title: 'Account Created Successfully',
          text: 'Welcome to Surely! Get ready to explore and make the most of Surely. Thank you for joining us!',
          startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
        });
      }
    } catch (error) {
      toast.show({
        title: 'Error',
        text: error?.message,
        startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
      });
    }
  };

  return (
    <div style={containerStyles}>
      {/* Header */}
      <header style={headerStyles}>
        <HeaderLogo />
      </header>

      {/* Main Layout */}
      <div style={mainLayoutStyles}>
        {/* Left Panel */}
        <div style={leftColumnStyles}>
          <div style={centerContentStyles}>
            <LogoIcon src={surelyLogo} alt="Surely Logo" style={logoStyles} />
            <TitleHeader>
              Let's get you started
            </TitleHeader>
            <SubtitleText>
              Tell us what you need and we will take care of the rest
            </SubtitleText>
          </div>
        </div>

        {/* Right Panel with left border */}
        <div style={rightColumnStyles}>
          <div style={{ 
            width: '100%',
            height: '100%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '40px 0'
          }}>
            <div style={{
              width: '100%',
              maxWidth: '364px',
              display: 'flex',
              flexDirection: 'column',
              gap: '50px',
              alignItems: 'center'
            }}>
              {/* Looking to hire */}
              <HireOptionButton
                isSelected={selectedType === 'client'}
                onClick={() => setSelectedType('client')}
              />
              
              {/* Looking for a job */}
              <JobOptionButton
                isSelected={selectedType === 'operative'}
                onClick={() => setSelectedType('operative')}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer style={{
        ...footerStyles,
        justifyContent: 'flex-end'
      }}>
        <div style={actionContainerStyles}>
          <PrimaryButton onClick={handleContinue}>
            Next
          </PrimaryButton>
        </div>
      </footer>
    </div>
  );
};

export default JobPostIntroCrucial;
