import React from 'react';
import { secondaryButtonStyles } from '../styles';

interface SecondaryButtonProps {
  children: React.ReactNode;
  onClick: () => void;
}

const SecondaryButton: React.FC<SecondaryButtonProps> = ({ children, onClick }) => {
  return (
    <button
      type="button"
      onClick={onClick}
      style={secondaryButtonStyles}
    >
      {children}
    </button>
  );
};

export default SecondaryButton;