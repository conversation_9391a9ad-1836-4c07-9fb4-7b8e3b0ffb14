// @ts-nocheck
import React, { useEffect, useState } from 'react';
import { Button, Text, View, Image } from 'reshaped';
import { useToastSystem } from 'src/context/ToastSystemContext';
import { useNavigate, useLocation } from 'react-router-dom';
import { headerLogo } from '../../assets/images';
import surelyproicon1 from '../../assets/icons/surelyproicon/surelyproicon1.svg';
import surelyproicon2 from '../../assets/icons/surelyproicon/surelyproicon2.svg';
import surelyproicon3 from '../../assets/icons/surelyproicon/surelyproicon3.svg';
import surelyproicon4 from '../../assets/icons/surelyproicon/surelyproicon4.svg';
import surelyproicon5 from '../../assets/icons/surelyproicon/surelyproicon5.svg';
import surelyproicon6 from '../../assets/icons/surelyproicon/surelyproicon6.svg';
import surelyproicon7 from '../../assets/icons/surelyproicon/surelyproicon7.svg';
import inclusivitypledgeicon from '../../assets/icons/inclusivitypledgeicon/inclusivitypledgeicon.svg';
import surleyicon from '../../assets/icons/surleyicon/surleyicon.png';

import { CircularProgressbar, buildStyles } from 'react-circular-progressbar';
import 'react-circular-progressbar/dist/styles.css';
import GradientSVG from './gradientSVG';
import { addSurelyProBadge } from 'src/services/settings';


interface SurelyProPointsProps {
  totalQuestions: number;
  correctAnswers: number;
}

const SurelyProPoints: React.FC<SurelyProPointsProps> = ({ totalQuestions, correctAnswers }) => {
  const toastSystem = useToastSystem();
  const location = useLocation();
  const { state } = useLocation();

  const { type, text, questionSet, document, downloadDocument, video } = state || {
    type: '',
    text: '',
    document: '',
    downloadDocument: '',
    video: '',
    questionSet :[]
  };

  const geticonSrc = (type: any) => {
    switch (type) {
      case 'CustomerService':
        return surelyproicon3;
      case 'UseOfEquipment':
        return surelyproicon2;
      case 'DisabilityFocus':
        return surelyproicon6;
      case 'SubstanceAwareness':
        return surelyproicon4;
      case 'VulnerablePeople':
        return surelyproicon5;
      case 'ConflictManagament':
        return surelyproicon7;
      case 'InclusivityPledge':
        return inclusivitypledgeicon;
      default:
        return surelyproicon1;
    }
  };
  const imageSrc = geticonSrc(type);

  const [totalQuestionsState] = useState<number>(location.state?.totalQuestions || totalQuestions);
  const [correctAnswersState] = useState<number>(location.state?.correctAnswers || correctAnswers);

  const percentage = totalQuestionsState !== 0 ? (correctAnswersState / totalQuestionsState) * 100 : 0;

  const [selectedType, setSelectedType] = useState<string | null>(null);
  const navigate = useNavigate();

  useEffect(() => {}, [totalQuestionsState, correctAnswersState]);
  const idCSS = 'hello';

  const handleSubmit = async () => {
    const saveSurelyProBadges: any = {
      type,
      text,
      score: percentage, // Include the score in the request
    };
    if (percentage === 100) {
      try {
        const response = await addSurelyProBadge(saveSurelyProBadges);
        if (response && !response.error) {
          toastSystem.showSuccess('Success!', 'Badge awarded successfully!');
        } else {
          toastSystem.showError('Error', response?.message || 'Failed to award badge. Please try again.');
        }
      } catch (error) {
        console.error('Error awarding badge:', error);
        toastSystem.showError('Error', 'Failed to award badge. Please try again.');
      }
    }
    navigate('/surelypro-traning');
  };

  useEffect(() => {
    if (percentage === 100) {
      toastSystem.showSuccess(
        'Congratulations!',
        `You have successfully completed the "${text}" exam and earned yourself a SurelyPro badge.`
      );
    } else if (percentage < 100 && percentage > 0) {
      toastSystem.showInfo(
        'Retake the test',
        'You did not earn the SurelyPro badge this time. We recommend carefully watching the video/presentation again and giving it another try.'
      );
    }
  }, [percentage, text]);

  return (
    <View className='flex flex-col w-[1320px] gap-4 p-4 md:p-8 lg:p-12 xl:p-16 2xl:p-20'>
      <View className='flex flex-col md:w-[536px] mx-auto'>
        <Text className='text-[#1A1A1A] text-center font-rufina-stencil text-[32px] font-normal leading-[40px]'>
          {percentage === 100 ? 'Congratulations!' : 'Retake the test'}
        </Text>
        <Text className='text-[#323C58] text-center rubik text-[16px] font-normal leading-[24px] '>
          {percentage === 100
            ? `You have successfully completed the ${text} course and earned yourself a new SurelyPro badge.`
            : 'You did not earn the SurelyPro badge this time. We recommend carefully watching the video/presentation again and giving it another try.'}
        </Text>
      </View>
      <View className='gap-2 justify-center items-center mx-auto'>
        <Button
          size='small'
          rounded={true}
          elevated={false}
          className={`px-2 py-1 ${
            type === 'InclusivityPledge' ? '!bg-[#ffff]' : '!bg-[#DDEFFF]'
          } text-xs max-w-xs overflow-hidden truncate mt-[20px]`}
        >
          {type === 'InclusivityPledge' ? (
            <View className='flex  h-[44px] p-[12px] justify-center items-center gap-[4px] self-stretch rounded-full border border-dark-gradient'>
              <img src={inclusivitypledgeicon} />
              <Text className='rubik font-normal leading-[20px] text-[16px]'>Inclusivity Pledge</Text>
            </View>
          ) : (
            <View className='flex flex-row justify-between'>
              <Image src={imageSrc} className='w-[16px] h-[16px] mt-[2px]' />
              <Text className='text-[#053D6D] rubik text-[12px] font-normal leading-[20px] ml-[5px]'>{text}</Text>
            </View>
          )}
        </Button>
      </View>
      <Text className='text-center rubik text-[20px]  font-normal text-[#323C58] mt-[52px]'>Your score</Text>

      <View className='w-[281.664px] h-[281.664px] flex-shrink-0 mx-auto mt-[20px]'>
        <GradientSVG />
        <CircularProgressbar
          strokeWidth={11}
          value={percentage}
          text={`${percentage}%`}
          styles={{
            path: { stroke: `url(#${idCSS})`, height: '100%' },
            trail: {
              stroke: '#C7CDDB',
            },
            text: { fill: '#323C58' },
          }}
        />
      </View>

      <View className='flex flex-col mt-[130px]'>
        <View className='flex w-full justify-between mt-[16px]'>
          {percentage < 100 && (
            <Button
              variant='outline'
              onClick={() => {
                navigate('/surelypro-traning-start-course', {
                  state: { type, text, questionSet, document, downloadDocument, video },
                });
              }}
              className='flex justify-center items-center self-stretch px-4 py-2 gap-2 self-stretch border border-neutral rounded-[8px] w-[150px] sm:w-[180px] h-[48px] '
            >
              <Text className='rubik text-[16px] text-[#0B80E7] font-medium leading-[24px] '>Take Again</Text>
            </Button>
          )}

          <Button
            endIcon={() => (
              <span className='material-icons-outlined p-0 pb-1 text-[20px] text-[#FFFFFF]'>arrow_forward_ios</span>
            )}
            onClick={handleSubmit}
            className='flex justify-center rubik items-center self-stretch px-4 py-2 gap-2 self-stretch border border-neutral rounded-[8px] !bg-[#0B80E7] w-[150px] sm:w-[180px] h-[48px] '
          >
            <Text className='rubik text-[16px] text-[#FFFFFF] font-medium leading-[24px] '> Finish</Text>
          </Button>
        </View>
        <View className='flex items-center justify-center'>
          <Image src={headerLogo} className='w-[109.76px] h-[41.274px] flex-shrink-0' />
        </View>
      </View>
    </View>
  );
};

export default SurelyProPoints;
