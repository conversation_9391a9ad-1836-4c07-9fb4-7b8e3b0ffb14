[2025-08-09 16:08:45] local.DEBUG: Message-ID: <5c4e9f0ec090338199e731b86cd9e465@127.0.0.1>
Date: Sat, 09 Aug 2025 16:08:45 +0000
Subject: Verify Your Surely Email
From: Surely <<EMAIL>>
To: <EMAIL>
MIME-Version: 1.0
Content-Type: text/html; charset=utf-8
Content-Transfer-Encoding: quoted-printable

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Your Surely App Account</title>
</head>
<body>
    <p>Dear dabega 8668,</p>


    <p>Thank you for choosing Surely! We're excited to have you on board and want to ensure the security of your account. To complete the registration process and gain full access to our features, please verify your email address.</p>


    <p>To verify your email, simply click on the link below:</p>


    <a href="http://localhost:5173/verify-email?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzM4NCJ9.******************************************************.kuZjfo2oE-6X4WOI-8vPeSwWngb3j4yeAYXn7rL0G6RNLpCqWuSbkLtK0FoEQCwV">Click here to verify your email</a>


    <p>If you are unable to click the link, please copy and paste it into your browser's address bar.</p>


    <p>Please note that this link is valid for the next 24 hours. After this period, you may need to request a new verification email.</p>


    <p>Thanks once again for your support. It really does make all the difference.</p>


    <p>Kind regards,<br>
    Aliona & Aurela</p>
</body>
</html>
  
[2025-08-09 16:10:38] local.ERROR: Client error: `GET http://api.postcodes.io/postcodes/00000` resulted in a `404 Not Found` response:
{"status":404,"error":"Invalid postcode"}
 {"userId":3066,"exception":"[object] (GuzzleHttp\\Exception\\ClientException(code: 404): Client error: `GET http://api.postcodes.io/postcodes/00000` resulted in a `404 Not Found` response:
{\"status\":404,\"error\":\"Invalid postcode\"}
 at C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\guzzlehttp\\guzzle\\src\\Exception\\RequestException.php:113)
[stacktrace]
#0 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response), NULL, Array, NULL)
#1 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\guzzlehttp\\promises\\src\\Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\guzzlehttp\\promises\\src\\Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\guzzlehttp\\promises\\src\\Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\guzzlehttp\\promises\\src\\Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\guzzlehttp\\promises\\src\\Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\guzzlehttp\\promises\\src\\Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\guzzlehttp\\promises\\src\\Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\app\\Http\\Controllers\\Api\\GeneralController.php(52): GuzzleHttp\\Client->request('GET', 'http://api.post...')
#11 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\GeneralController->update(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('update', Array)
#13 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\GeneralController), 'update')
#14 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#15 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#16 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(93): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#21 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(55): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#22 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#23 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#25 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#31 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(149): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(32): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#45 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\barryvdh\\laravel-cors\\src\\HandleCors.php(52): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#66 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#68 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\server.php(21): require_once('C:\\\\Users\\\\<USER>\\\\...')
#69 {main}
"} 
[2025-08-09 21:07:40] local.DEBUG: Message-ID: <b0a513f4c5d645335b9097abb3aa9f99@127.0.0.1>
Date: Sat, 09 Aug 2025 21:07:40 +0000
Subject: Verify Your Surely Email
From: Surely <<EMAIL>>
To: <EMAIL>
MIME-Version: 1.0
Content-Type: text/html; charset=utf-8
Content-Transfer-Encoding: quoted-printable

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Your Surely App Account</title>
</head>
<body>
    <p>Dear nojeme 6798,</p>


    <p>Thank you for choosing Surely! We're excited to have you on board and want to ensure the security of your account. To complete the registration process and gain full access to our features, please verify your email address.</p>


    <p>To verify your email, simply click on the link below:</p>


    <a href="http://localhost:5173/verify-email?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzM4NCJ9.******************************************************.iJhe7IQioDQKW1rBUPKZgY6KbjWvy4SzqUhMeMU7V2SP2bmdpU4YMS70SH_riJoX">Click here to verify your email</a>


    <p>If you are unable to click the link, please copy and paste it into your browser's address bar.</p>


    <p>Please note that this link is valid for the next 24 hours. After this period, you may need to request a new verification email.</p>


    <p>Thanks once again for your support. It really does make all the difference.</p>


    <p>Kind regards,<br>
    Aliona & Aurela</p>
</body>
</html>
  
[2025-08-09 21:30:21] local.DEBUG: Message-ID: <62bea3acaf61ae712effc8a61a758fdf@127.0.0.1>
Date: Sat, 09 Aug 2025 21:30:21 +0000
Subject: Verify Your Surely Email
From: Surely <<EMAIL>>
To: <EMAIL>
MIME-Version: 1.0
Content-Type: text/html; charset=utf-8
Content-Transfer-Encoding: quoted-printable

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Your Surely App Account</title>
</head>
<body>
    <p>Dear nojeme 6798,</p>


    <p>Thank you for choosing Surely! We're excited to have you on board and want to ensure the security of your account. To complete the registration process and gain full access to our features, please verify your email address.</p>


    <p>To verify your email, simply click on the link below:</p>


    <a href="http://localhost:5173/verify-email?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzM4NCJ9.******************************************************.w9P44R3g3AHDh7jWGGarMCdXukyWq1KTwiV8X9S_0tRTeZfUxa3q3BBsjVrvR9TL">Click here to verify your email</a>


    <p>If you are unable to click the link, please copy and paste it into your browser's address bar.</p>


    <p>Please note that this link is valid for the next 24 hours. After this period, you may need to request a new verification email.</p>


    <p>Thanks once again for your support. It really does make all the difference.</p>


    <p>Kind regards,<br>
    Aliona & Aurela</p>
</body>
</html>
  
[2025-08-09 21:30:21] local.INFO: Verification email sent successfully {"user_id":3067,"email":"<EMAIL>","token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzM4NCJ9.******************************************************.w9P44R3g3AHDh7jWGGarMCdXukyWq1KTwiV8X9S_0tRTeZfUxa3q3BBsjVrvR9TL"} 
