// @ts-nocheck
import React, { useState, useEffect, useContext } from 'react';
import { View, Image, Text, Button } from 'reshaped';
import { useNavigate } from 'react-router-dom';

import surleyicon from '../../assets/icons/surleyicon/surleyicon.png';
import { getValidateOperatorProfile } from 'src/services/user';
import { AppContext } from '../../context/AppContext';

const ClientValidateToast: React.FC = ({ showCardToast } : {showCardToast: any}) => {
  const navigate = useNavigate();
  const [showToast, setShowToast] = useState(true);

//   const {
//     addressVerificationDocument,
//     documentType,
//     idBackDocument,
//     idFrontDocument,
//     selfieVerificationDocument,
//     siaLicenceCardPhoto,
//     siaLicenceExpiryDate,
//     siaLicenceNumber,
//   } = useContext(AppContext);

//   const isValidateUnavailable =
//     addressVerificationDocument === null ||
//     documentType === null ||
//     idBackDocument === null ||
//     idFrontDocument === null ||
//     selfieVerificationDocument === null ||
//     siaLicenceCardPhoto === null ||
//     siaLicenceExpiryDate === null ||
//     siaLicenceNumber === null ||
//     addressVerificationDocument === undefined ||
//     documentType === undefined ||
//     idBackDocument === undefined ||
//     idFrontDocument === undefined ||
//     selfieVerificationDocument === undefined ||
//     siaLicenceCardPhoto === undefined ||
//     siaLicenceExpiryDate === undefined ||
//     siaLicenceNumber === undefined;

  return (
    <View>
      {showToast && (
        <View className={`fixed bottom-1 sm:bottom-7 right-[15.3%] sm:right-5 transform translate-y-0 ${showCardToast ? 'sm:bottom-16 opacity-95' : ''} w-[320px] sm:w-[420px] mt-[26px] z-10`}>
          <View className='w-[370px] sm:w-[420px] bg-[#1C212BF7] rounded-[8px] text-white '>
            <button
              className='flex items-center justify-end ml-auto bg-transparent border border-transparent hover:border-transparent'
              onClick={() => setShowToast(false)}
            >
              <span className='material-icons align-middle text-500 text-[#fff]'>
                close
              </span>
            </button>
            <View className='flex items-start gap-4 mt-[-12px] ml-5 mr-4'>
              <Image
                src={surleyicon}
                alt='Surely Icon'
                className='w-[64px] h-[64px]'
              />
              <View className='text-left w-[308px] mb-[20px]'>
                <Text className='text-[#EFF0F1] rubik text-[14px] leading-[20px] font-bold'>
                  Welcome to our platform!
                </Text>
                <Text className='text-[#EFF0F1] rubik text-[14px] font-normal leading-[20px]'>
                  Complete your company validation to start hiring security operatives. Please provide your business details to get started.
                </Text>
                <Button
                  className='flex justify-center items-center border border-neutral rounded-[8px] bg-background-base !text-white text-[14px] !bg-[#30374A] w-[173px] mt-2.5'
                  onClick={() => navigate('/first-step-validation-client')}
                >
                  Validate your profile
                </Button>
              </View>
            </View>
          </View>
        </View>
      )}
    </View>
  );
};

export default ClientValidateToast;
