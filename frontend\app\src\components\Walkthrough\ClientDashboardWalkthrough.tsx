import React, { useState, useEffect, useCallback } from 'react';

interface Props {
  visible: boolean;
  onComplete: () => void;
  images: string[];
}

const ClientDashboardWalkthrough: React.FC<Props> = ({ visible, onComplete, images }) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  // Reset to first image when walkthrough becomes visible
  useEffect(() => {
    if (visible) {
      setCurrentIndex(0);
    }
  }, [visible]);

  // Handle keyboard navigation
  useEffect(() => {
    if (!visible) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onComplete();
      } else if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        advance();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [visible, currentIndex, images.length, onComplete]);

  const advance = useCallback(() => {
    if (currentIndex < images.length - 1) {
      setCurrentIndex(prev => prev + 1);
    } else {
      onComplete();
    }
  }, [currentIndex, images.length, onComplete]);

  const handleBackdropClick = (e: React.MouseEvent) => {
    // Don't advance if clicking the skip button
    if ((e.target as HTMLElement).closest('.skip-button')) {
      return;
    }
    advance();
  };

  if (!visible || !images.length) {
    return null;
  }

  const currentImage = images[currentIndex];

  return (
    <div 
      className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-70 cursor-pointer"
      onClick={handleBackdropClick}
    >
      {/* Skip button */}
      <button
        className="skip-button fixed top-4 right-4 z-[10000] bg-white text-black px-4 py-2 rounded-md font-medium shadow-lg hover:bg-gray-100 transition-colors"
        onClick={(e) => {
          e.stopPropagation();
          onComplete();
        }}
      >
        Skip
      </button>

      {/* Image container */}
      <div className="relative w-screen h-screen flex items-center justify-center">
        <img
          src={currentImage}
          alt={`Walkthrough step ${currentIndex + 1} of ${images.length}`}
          className="max-w-full max-h-full object-contain"
          style={{ pointerEvents: 'none' }}
          onError={() => {
            console.error('Failed to load walkthrough image:', currentImage);
            // Try to advance to next image or complete if this was the last one
            advance();
          }}
        />
      </div>

      {/* Progress indicator */}
      <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
        {images.map((_, index) => (
          <div
            key={index}
            className={`w-2 h-2 rounded-full ${
              index === currentIndex ? 'bg-white' : 'bg-white bg-opacity-50'
            }`}
          />
        ))}
      </div>

      {/* Instructions */}
      <div className="fixed bottom-16 left-1/2 transform -translate-x-1/2 text-white text-center">
        <p className="text-sm">
          Click anywhere to continue • Press ESC to skip • {currentIndex + 1} of {images.length}
        </p>
      </div>
    </div>
  );
};

export default ClientDashboardWalkthrough;
