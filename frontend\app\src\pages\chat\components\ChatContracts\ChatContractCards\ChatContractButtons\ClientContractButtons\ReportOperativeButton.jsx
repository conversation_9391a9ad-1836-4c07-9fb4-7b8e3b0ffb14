import { useToggle } from 'reshaped';
import ReportOperatorModal from 'src/components/Profile/ClientProfileComponents/ReportOperatorModal/ReportOperatorModal';
// import AskForSupportClientModal from '../../../../ChatModals/AskForSupportClientModal';

const ReportOperativeButton = ({ operator, chat }) => {
  const { active, activate, deactivate } = useToggle(false);

  return (
    <>
      <div
        onClick={activate}
        className='w-full flex cursor-pointer items-center justify-center gap-2 rounded-[8px] border border-[#DFE2EA] py-[10.5px]'
      >
        <p className='rubik flex items-center gap-2 whitespace-nowrap text-[16px] font-medium leading-[24px] text-[#CB101D]'>
          <span className='material-icons'>report</span>
          Report
        </p>
      </div>
      <ReportOperatorModal active={active} deactivate={deactivate} oper={operator} />
    </>
  );
};

export default ReportOperativeButton;
