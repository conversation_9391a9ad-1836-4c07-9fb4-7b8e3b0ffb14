import React from 'react';
import { labelStyles } from '../../JobPostIntro/styles';

interface Option {
  value: string;
  label: string;
}

interface FormSelectTwoProps {
  label: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  options: Option[];
  placeholder?: string;
  caretIcon?: string;
  marginBottom?: string | number;
}

const FormSelectTwo: React.FC<FormSelectTwoProps> = ({
  label,
  value,
  onChange,
  options,
  placeholder = 'Select an option',
  caretIcon,
  marginBottom
}) => {
  const selectContainerStyles: React.CSSProperties = {
    marginBottom: marginBottom || '24px',
  };

  const selectStyles: React.CSSProperties = {
    width: '100%',
    padding: '16px',
    border: '1px solid #ddd',
    borderRadius: '8px',
    fontSize: '16px',
    fontFamily: 'Rubik, sans-serif',
    appearance: 'none',
    backgroundColor: 'white',
  };

  const caretContainerStyles: React.CSSProperties = {
    position: 'absolute',
    right: '16px',
    top: '50%',
    transform: 'translateY(-50%)',
    pointerEvents: 'none',
  };

  return (
    <div style={selectContainerStyles}>
      <label style={labelStyles}>
        {label}
      </label>
      <div style={{ position: 'relative', marginTop: '8px' }}>
        <select
          value={value}
          onChange={onChange}
          style={selectStyles}
        >
          <option value="" disabled>
            {placeholder}
          </option>
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {caretIcon && (
          <div style={caretContainerStyles}>
            <img src={caretIcon} alt="Caret Icon" style={{ width: '16px', height: '16px' }} />
          </div>
        )}
      </div>
    </div>
  );
};

export default FormSelectTwo;
