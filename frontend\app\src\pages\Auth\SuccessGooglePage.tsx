// @ts-nocheck
import { useContext, useEffect } from "react"
import { useNavigate } from "react-router-dom";
import { useToastSystem } from 'src/context/ToastSystemContext';
import { AuthContext } from "src/context/AuthContext";
import { loginUser } from "src/services/user";
import { Image } from "reshaped";
import surleyicon from '../../assets/icons/surleyicon/surleyicon.png';

export const SuccessGooglePage = () => {
  const navigate = useNavigate();
  const { authenticateUser } = useContext(AuthContext);
  const toastSystem = useToastSystem()
  const searchParams = new URLSearchParams(window.location.href);

  const isDataUnavailable = (userData: any) => {
    return (
      userData.user.profile_title === null ||
      userData.user.profile_description === null ||
      userData.user.profile_description === undefined ||
      userData.user.profile_photo === null ||
      userData.user.profile_video === null ||
      userData.languages.length === 0 ||
      userData.user.profile_title === undefined ||
      userData.user.profile_photo === undefined ||
      userData.user.profile_video === undefined ||
      userData.employments.length === 0 ||
      userData.qualifications.length === 0
    );
  };

  useEffect(() => {
    const accessToken = searchParams.get('access_token');
    if (accessToken && accessToken !== '') {
      localStorage.setItem('google_token', accessToken);
    } else {
      navigate('/');
    }

    loginUser({ 
      google_token: accessToken,
      email: '',
      password: '',
      password_confirmation: '',
      loginType: 1,
      platform: 3,
      appVersion: "1.0",
      firebaseToken: 'test',
      phone: '***********',
      account_type: '', 
    }).then((loginResponse) => {
      if (loginResponse?.token) {
        authenticateUser(loginResponse);
        if (loginResponse?.data.user.account_type == '2') {
          navigate('/client-dashboard');
        } else {
          if (isDataUnavailable(loginResponse.data)) {
            navigate('/my-profile');
          } else {
            navigate('/search-jobs');
          }
        }
      }
      else{
        navigate("/")
        toastSystem.showError('Error.', 'User not found!');
      }
      
    })
    
  }, [])

  return (<></>)
}