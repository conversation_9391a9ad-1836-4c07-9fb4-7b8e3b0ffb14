<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Auth\ForgotPasswordController;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\BusinessProfileRequest;
use App\Http\Requests\Api\ConfirmResetPasswordRequest;
use App\Http\Requests\Api\RegisterVerificationRequest;
use App\Http\Requests\Api\FreelancerProfileRequest;
use App\Http\Requests\Api\LoginRequest;
use App\Http\Requests\Api\PasswordRequest;
use App\Http\Requests\Api\RegisterRequest;
use App\Http\Requests\Api\SendCodeRequest;
use App\Http\Requests\Api\SendResetPasswordRequest;
use App\Http\Requests\Api\VerifyCodeRequest;
use App\Http\Resources\ProfileResource;
use App\Mail\DocumentVerification;
use App\Mail\RegisterMail;
use App\Models\City;
use App\Models\ExpertiseField;
use App\Models\Gig;
use App\Models\MobileUser;
use App\Models\Report;
use App\Models\SiaCertificate;
use App\Models\User;
use App\Notifications\AccountDeletedNotification;
use App\Notifications\DocumentVerificationNotification;
use App\Notifications\ForgotPasswordNotification;
use App\Notifications\RegisterNotification;
use App\Notifications\ResetPasswordNotification;
use App\Notifications\VerifyEmailNotification;
use Carbon\Carbon;
use ErrorException;
use Exception;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use GuzzleHttp\Client;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Exceptions\InvalidSignatureException;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Laravel\Passport\Passport;
use Laravel\Sanctum\PersonalAccessToken;
use Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class AuthController extends Controller
{
    use \App\Traits\Helpers;

    public const REGISTRATION_DOCUMENTS = [
        'sia_licence_card_photo',
        'id_front_document',
        'id_back_document',
        'address_verification_document',
        'selfie_verification_document'
    ];

    private function userObj($user)
    {
        $obj = [
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'email_verified' => $user->email_verified,
            'phone' => $user->phone,
            'account_type' => $user->account_type ?? "",
            'age' => $user->age ?? 0,
            'height' => (string)$user->height ?? "",
            'gender' => (string)$user->gender ?? "",
            'pay_rate' => $user->pay_rate ?? 0,
            'years_of_experience' => (int)$user->years_of_experience ?? 0,
            'company_registration' => $user->company_registration ?? "",
            'expertise_id' => $user->expertise_id ?? 0,
            'expertise_name' => $user->expertise ? $user->expertise->name : '',
            'address1' => $user->address1?? "",
            'address2' => $user->address2?? "",
            'address_1' => $user->address_1 ?? "",
            'address_2' => $user->address_2 ?? "",
            'postal_code' => $user->postal_code?? "",
            'website' => $user->website?? "",
            'city_id' => $user->city_id ?? 0,
            'city' => $user->city ?? "",
            'lat' => $user->lat ?? "",
            'lng' => $user->lng ?? "",
            'language' => $user->language
        ];

        return $obj;
    }

    public function check_email(Request $request): JsonResponse
    {
        if (!$request->filled('email')) {
            return response()->json([
                'error' => true,
                'message' => 'Email is required!',
            ]);
        }

        if (MobileUser::where('email', $request->get('email'))->first()) {
            return response()->json([
                'error' => true,
                'message' => 'Email is used before!',
            ]);
        }

        return response()->json([
            'error' => false,
            'message' => 'Email is free to use!',
        ]);
    }

    public function register(RegisterRequest $request): JsonResponse
    {
        $data = $request->all();
        $data['status'] = true;
        // Set initial email verification status and deadline
        $data['email_verified'] = false;
        $data['email_verification_deadline'] = now()->addDays(7);
        
        if (!$request->has('ref')) {
            return response()->json([
                'error' => true, 
                'message' => 'Ref should be present in request!',
            ]);
        }

        if ($request->get('ref') == 'register' && isset($data)) {
            // Hash the password before creating the user
            $data['password'] = bcrypt($data['password']);
            $user = MobileUser::create($data);
            
            // Your existing email verification token generation
            $secret = 'surely_security';
            $data = [
                'user_id' => $user->id,
                'expires_at' => time() + 3600,
            ];

            if ($request->filled('referal_code')) {
                $data['referal_code'] = $request->get('referal_code');
            }

            $token = JWT::encode($data, $secret, 'HS384');
            $verificationLink = config('app.front_url') . '/verify-email?token=' . $token;
            $user->email_token = $token;
            $user->save();

            // Still send the verification email but don't block access
            try {
                $user->notify(new VerifyEmailNotification($verificationLink, $user->name));
            } catch (Exception $e) {
                \Log::error('Email verification error: ' . $e->getMessage());
            }

            // Return success response with auth token
            return response()->json([
                'error' => false,
                'message' => 'Registration successful! Please verify your email within 7 days.',
                'token' => $user->createToken('auth_token')->accessToken,
                'data' => $this->userObj($user),
                'redirect' => $user->account_type === 1 ? '/my-profile' : '/client-dashboard'
            ]);
        }

        if ($request->filled('google_token')) {
            //$client = new \Google_Client();
            //$client->setClientId(config('services.google.client_id'));
            //$client->setClientSecret(config('services.google.client_secret'));
            //$client->setRedirectUri(config('services.google.redirect_url'));
            //$client->addScope("https://www.googleapis.com/auth/userinfo.profile");

            $token = $request->input('google_token');

            try {
                $url = 'https://www.googleapis.com/oauth2/v3/userinfo';
                $options = [
                    'http' => [
                        'header' => "Authorization: Bearer $token",
                        'method'  => 'GET',
                    ],
                ];

                $context = stream_context_create($options);
                $response = file_get_contents($url, false, $context);

                if ($response === FALSE) {
                    // Handle error
                    throw new Exception('Access token is expired.');
                }

                $userInfo = json_decode($response);

                // Check if email is present in the response
                if (!property_exists($userInfo, 'email')) {
                    throw new Exception('Email is not available. Ensure the correct scope is requested during authentication.');
                }

                $data['email'] = $userInfo->email;
                $data['name'] = $userInfo->name;
                $data['social_id'] = $userInfo->sub;
                // You can access other user data like profile picture, etc. using properties of $google_account_info
            } catch (Exception $e) {
                return response()->json([
                    'error' => true,
                    'message' => 'Error verifying Google Token: ' . $e->getMessage(),
                    'token' => $token,
                    'data' => [
                        'client' => config('services.google.client_id'),
                        'secret' => config('services.google.client_secret'),
                        'redirect' => config('services.google.redirect_url'),
                        'scopes' => [],
                    ],
                ]);
            }
        }
        else if ($request->filled('linkedin_token')) {

            $client = new \GuzzleHttp\Client();

            $response = $client->request('POST', 'https://www.linkedin.com/oauth/v2/accessToken', [
                'form_params' => [
                    'grant_type' => 'authorization_code',
                    'code' =>  $request->linkedin_token,
                    'redirect_uri' => $request->redirect_uri,
                    'client_id' => env('LINKEDIN_CLIENT_ID'),
                    'client_secret' => env('LINKEDIN_CLIENT_SECRET'),
                ]
            ]);

            $response = json_decode($response->getBody()->getContents());

            $access_token = $response->access_token;

            $userResponse = $client->request('GET', 'https://api.linkedin.com/v2/userinfo', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $access_token,
                ]
            ]);

            $userInfo = json_decode($userResponse->getBody()->getContents());

            $data['email'] = $userInfo->email;
            $data['name'] = $userInfo->name;
            $data['social_id'] = $userInfo->sub;

        }
        else{
            $data['password'] = bcrypt($data['password']);
        }

        if($request->filled('google_token') || $request->filled('linkedin_token')){
            if(MobileUser::where('social_id', $data['social_id'])->exists()){
                return response()->json([
                    'error' => true,
                    'message' => 'User already exists!',
                    'token' => '',
                    'data' => []
                ]);
            }
        }

        $user = MobileUser::where('email', $data['email'])
            ->withTrashed()
            ->exists();
            
        if($user) {
            return response()->json([
                'error' => true,
                'message' => 'Use another email to create account!',
                'token' => '',
                'data' => []
            ]);
        }

        if (!empty($data['postal_code'])) {
            try {
                $client = new Client();

                $response = $client->request('GET', "http://api.postcodes.io/postcodes/" . $data['postal_code']);
                $dataPostcode = $response->getBody()->getContents();
                $dataPostcode = json_decode($dataPostcode);
            } catch (Exception $e) {
                return response()->json([
                    'error' => true,
                    'message' => 'Invalid postcode! Enter the correct postal code to get the correct location!',
                    'token' => '',
                    'data' => []
                ]);
            }
            $data['lat'] = $dataPostcode->result->latitude;
            $data['lng'] = $dataPostcode->result->longitude;
        }
        
        return $this->send_verification_email($request, $data);
    }


    public function send_verification_email(Request $request, $data = null)
    {
        if (!$request->has('ref')) {
            return response()->json([
                'error' => true, 
                'message' => 'Ref should be present in request!',
            ]);
        }

        if ($request->get('ref') == 'register' && isset($data)) {
            $user = MobileUser::create($data);
            if (!$user) {
                return response()->json([
                    'error' => true,
                    'message' => 'Cannot create user!',
                ]);
            }
            $referal = $this->generateReferalToken([
                'id' => $user->id,
                'email' => $user->email,
                'name' => $user->name,
            ]);

            $user->referal_code = $referal['code'];
            $user->referal_token = $referal['token'];
            $user->save();
        } 
        
        if ($request->get('ref') == 'login' && $request->has('email')) {
            $user = MobileUser::where('email', $request->get('email'))->first();
            if (!$user) {
                return response()->json([
                    'error' => true,
                    'message' => 'Use not found with this email!',
                ]);
            }
        }

        if (!isset($user)) {
            return response()->json([
                'error' => true,
                'message' => 'User does not exist!',
            ]);
        }

        $secret = 'surely_security';
        $data = [
            'user_id' => $user->id,
            'expires_at' => time() + 3600, // Expires in 1 hour
        ];

        if ($request->filled('referal_code')) {
            $data['referal_code'] = $request->get('referal_code');
        }

        $token = JWT::encode($data, $secret, 'HS384');

        $verificationLink = config('app.front_url') . '/verify-email?token=' . $token;
        $user->email_token = $token;
        $user->save();

        try {
            $user->notify(new VerifyEmailNotification($verificationLink, $user->name));
            \Log::info('Verification email sent successfully', [
                'user_id' => $user->id,
                'email' => $user->email,
                'token' => $token
            ]);
        } catch (Exception $e) {
            \Log::error('Email verification error: ' . $e->getMessage()); 
            return response()->json([
                'error' => true,
                'message' => 'Error sending verification link to ' . $user->email,
                'debug' => [
                'email_token' => $token,
                'referal_token' => $user->referal_token ?? null,
            ],
            ]);
        }

        return response()->json([
            'error' => false,
            'message' => 'Please check your email at ' . $user->email . ' for the verification link!',
            'debug' => [
                'email_token' => $token,
                'referal_token' => $user->referal_token ?? null,
            ],
        ]);
    }

    public function verify_email(Request $request) 
    {
        \Log::info('verify_email function called.');
    
        if (!$request->has('token')) {
            \Log::warning('No token provided in request.');
            return response()->json([
                'error' => true,
                'message' => 'No token provided in request!',
            ]);
        }
    
        $token = $request->get('token');
        \Log::info('Token retrieved from request.', ['token' => $token]);
    
        $secret = 'surely_security';
        try {
            \Log::info('Attempting to decode token.');
            $data = JWT::decode($token, new Key($secret, 'HS384'));
            \Log::info('Token successfully decoded.', ['decoded_data' => (array) $data]);
    
            $user = MobileUser::find($data->user_id);
            \Log::info('Fetched user from database.', [
                'user_id' => $data->user_id,
                'user_email_token' => $user->email_token ?? 'null'
            ]);
    
            if ($user->email_token != $token) {
                \Log::error('Token mismatch.', [
                    'expected_token' => $user->email_token,
                    'received_token' => $token,
                ]);
                return response()->json([
                    'error' => true,
                    'message' => 'Unauthenticated user!',
                    'debug' => [
                        'expected_token' => $user->email_token,
                        'received_token' => $token,
                    ],
                ], 401);
            }
    
            if ($data->expires_at < Carbon::now()->timestamp) {
                \Log::warning('Token has expired.', [
                    'token_expiry' => $data->expires_at,
                    'current_time' => Carbon::now()->timestamp
                ]);
                return response()->json([
                    'error' => true,
                    'message' => 'Unauthenticated user! Token has expired!',
                ], 401);
            }
    
            if (property_exists($data, 'referal_code')) {
                \Log::info('Referal code found, decoding.', ['referal_code' => $data->referal_code]);
                $referalToken = $this->decodeReferalToken($data->user_id, $data->referal_code);
    
                if (!$referalToken['error']) {
                    $referalDiscount = Carbon::now()->addDays($referalToken['data']->discount_valid_days);
                    $referalName = $referalToken['data']->referent_name;
                    \Log::info('Referal token decoded.', [
                        'referal_discount' => $referalDiscount,
                        'referent_name' => $referalName,
                    ]);
                }
            }
    
            \Log::info('Creating new access token for user.');
            $accessToken = $user->createToken('5at')->accessToken;
            $user->email_token = null;
            $user->email_verified = true;
            $user->save();
            \Log::info('User email verified and token cleared.', ['user_id' => $user->id]);
    
            $response = [
                'error' => false,
                'message' => 'Success',
                'token' => $accessToken,
                'data' => $this->userObj($user),
            ];
    
            if (isset($referalDiscount) && isset($referalName)) {
                $response['referal_discount'] = $referalDiscount;
                $response['referent_name'] = $referalName;
            }
    
            \Log::info('Verification process completed successfully.', $response);
            return response()->json($response);
        } catch (ErrorException $e) {
            \Log::error('Error occurred during token verification.', ['exception' => $e->getMessage()]);
            return response()->json([
                'error' => true,
                'message' => 'Unauthenticated user! Invalid token!' . $e,
            ], 401);
        } catch (\Firebase\JWT\ExpiredException $e) {
            \Log::warning('JWT token expired.', ['exception' => $e->getMessage()]);
            return response()->json([
                'error' => true,
                'message' => 'Token has expired!',
            ], 401);
        } catch (\Firebase\JWT\SignatureInvalidException $e) {
            \Log::error('JWT signature is invalid.', ['exception' => $e->getMessage()]);
            return response()->json([
                'error' => true,
                'message' => 'Invalid key to decrypt token!',
            ], 401);
        }
    }

    public function generateReferalToken($data)
    {
        $secret = 'surely_security';
        $data = [
            'referent_id' => $data['id'],
            'referent_email' => $data['email'],
            'referent_name' => $data['name'],
            'discount_valid_days' => 30
        ];
        $token = JWT::encode($data, $secret, 'HS384');
        
        $randomBytes = random_bytes(6);
        $randomString = bin2hex($randomBytes);
        $code = substr($randomString, 0, 6);
        return [
            'code' => $code,
            'token' => $token,
        ];
    }

    public function getReferalLink()
    {
        $user = auth()->user();
        $referalToken = $user->referal_code;
        
        if (!$referalToken) {
            $data = [
                'id' => $user->id,
                'email' => $user->email,
                'name' => $user->name,
            ];
            $referal = $this->generateReferalToken($data);
           
            $user->referal_code = $referal['code'];
            $user->referal_token = $referal['token'];
            $user->save();
        }

        $referalLink = config('app.front_url') . '?action=sign_up&referal_code=' . $user->referal_code;

        return response()->json([
            'error' => false,
            'data' => $referalLink, 
        ]);
    }

    public function decodeReferalToken($id, $code)
    {
        $secret = 'surely_security';
        try {
            $token = MobileUser::where('referal_code', $code)->first();
            $token = $token->referal_token;
            
            if (!$token) {
                return [
                    'error' => true,
                    'message' => 'Invalid referal token!',
                ]; 
            }

            $data = JWT::decode($token, new Key($secret, 'HS384'));
            
            $user = MobileUser::where('email', $data->referent_email)->first();

            if (!$user) {
                return [
                    'error' => true,
                    'message' => 'Invalid referal token!',
                ]; 
            }

            if ($user->referal_token != $token) {
                return [
                    'error' => true,
                    'message' => 'Invalid referal token',
                ];
            }

            if ($id == $user->id) {
                return [
                    'error' => true,
                    'message' => 'Invalid referal token',
                ];
            }

            return [
                'error' => false,
                'data' => $data,
            ];
        } catch(ErrorException $e) {
            return $e;
        } catch(\Firebase\JWT\ExpiredException $e) {
            return $e;
        } catch (\Firebase\JWT\SignatureInvalidException $e){
            return $e;
        }
    }

    public function send_code(SendCodeRequest $request): JsonResponse
    {
        try {
            if (auth()->guard('api')->user()) {
                $user = auth()->guard('api')->user();
            } else {
                $user = MobileUser::where('phone', $request->phone)->first();
            }

            if ($user) {
                $now = Carbon::now();
                $sentAt = $user->sent_at ? Carbon::createFromFormat('Y-m-d H:i:s', $user->sent_at) : null;
                $diff_in_minutes = $sentAt ? $sentAt->diffInMinutes($now) : null;

                if (env('SEND_CODE_ACTIVE') ?? true) {
//                    if ($user->attempts > 6 && $diff_in_minutes <= 30) {
//                        return response()->json(['error' => true, 'message' => trans('lang.sms_limit'), 'user_id' => -1]);
//                    }
                }else{

                }

                $code = rand(100000, 999999);

                if (env('SEND_CODE_ACTIVE') ?? true) {
                    $client = new \MessageBird\Client(env('MESSAGE_BIRD_API_KEY') ?? '*************************');
                    $message = new \MessageBird\Objects\Message;
                    $message->originator = env('APP_NAME') ?? 'Surely';
                    $message->recipients = $user->phone;
                    $message->body = 'Your verification code is : ' . $code;
                    $client->messages->create($message);
                }

                $user->update([
                    'code' => $code,
                    'attempts' => $user->attempts + 1,
                    'sent_at' => $now->format('Y-m-d H:i:s'),
                ]);

                return response()->json(['error' => false, 'message' => trans('lang.success'), 'user_id' => $user->id]);
            } else {
                return response()->json(['error' => true, 'message' => trans('lang.user_not_found'), 'user_id' => -1]);
            }
        } catch (Exception $e) {
            return response()->json(['error' => true, 'message' => trans('lang.error'), 'user_id' => -1]);
        }
    }

    public function verify_code(VerifyCodeRequest $request): JsonResponse
    {
        $user = MobileUser::find($request->user_id);

        if ($user) {

            if (env('SEND_CODE_ACTIVE')) {
                $code = $request->code;

                if ($user->code !== $code) {
                    return response()->json(['error' => true, 'message' => trans('lang.wrong_code'), 'token' => '', 'data' => (object) []]);
                }
                if ($user->sent_at < Carbon::now()->subMinutes(30)) {
                    return response()->json(['error' => true, 'message' => trans('lang.code_expired'), 'token' => '', 'data' => (object) []]);
                }
            }

            $user->update(['firebase_token' => $request->firebase_token,
                'platform' => $request->platform, 'login_type' => $request->login_type,
                'app_version' => $request->app_version, 'attempts' => 0]);

            $token = $user->createToken('Scegli_lora')->accessToken;

            $userAllData = MobileUser::with(['employments', 'qualifications', 'siaCertificate', 'languages'])->find($user->id);

            return response()->json([
                'error' => false,
                'message' => 'Success',
                'token' => $token,
                'data' => new ProfileResource($userAllData),
                'session' => [
                    'expires_in' => config('session.lifetime') * 60, 
                    'warning_threshold' => 300 
                ]
            ]);
        } else {
            return response()->json([
                'error' => true,
                'message' => trans('lang.incorrect_code'),
                'token' => '',
                'data' => []
            ]);
        }
    }

    public function login(LoginRequest $request): ?JsonResponse
    {
        if ($request->filled('google_token')) {
            $token = $request->input('google_token');
            try {
                $url = 'https://www.googleapis.com/oauth2/v3/userinfo';
                $options = [
                    'http' => [
                        'header' => "Authorization: Bearer $token",
                        'method' => 'GET',
                    ],
                ];
                $context = stream_context_create($options);
                $response = file_get_contents($url, false, $context);

                if ($response === FALSE) {
                    // Handle error
                    throw new Exception('Access token is expired.');
                }

                $userInfo = json_decode($response);

                $data['email'] = $userInfo->email;
                $data['name'] = $userInfo->name;
                $data['social_id'] = $userInfo->sub;
            }
            catch (Exception $e) {
                return response()->json([
                    'error' => true,
                    'message' => 'Error verifying Google Token: ' . $e->getMessage(),
                ]);
            }
        }

        if ($request->filled('linkedin_token')) {

            $client = new \GuzzleHttp\Client();

            $response = $client->request('POST', 'https://www.linkedin.com/oauth/v2/accessToken', [
                'form_params' => [
                    'grant_type' => 'authorization_code',
                    'code' =>  $request->linkedin_token,
                    'redirect_uri' => $request->redirect_uri,
                    'client_id' => env('LINKEDIN_CLIENT_ID'),
                    'client_secret' => env('LINKEDIN_CLIENT_SECRET'),
                ]
            ]);

            $response = json_decode($response->getBody()->getContents());

            $access_token = $response->access_token;

            $userResponse = $client->request('GET', 'https://api.linkedin.com/v2/userinfo', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $access_token,
                ]
            ]);

            $userInfo = json_decode($userResponse->getBody()->getContents());

            $data['email'] = $userInfo->email;
            $data['name'] = $userInfo->name;
            $data['social_id'] = $userInfo->sub;

        }

        if($request->filled('google_token') || $request->filled('linkedin_token')){
            $user = MobileUser::where('social_id', $data['social_id'])->first();

            if (!$user) {
                return response()->json([
                    'error' => true,
                    'message' => 'User not found!',
                    'token' => '',
                    'data' => [],
                ]);
            }

            $token = $user->createToken('Scegli_lora')->accessToken;
            $userAllData = MobileUser::with(['employments', 'qualifications', 'siaCertificate', 'languages'])->find($user->id);
            return response()->json([
                'error' => false,
                'message' => 'Unauthorized access',
                'token' => $token,
                'data' => new ProfileResource($userAllData),
                'session' => [
                    'expires_in' => config('session.lifetime') * 60, // Convert minutes to seconds
                    'warning_threshold' => 300 // 5 minutes warning
                ]
            ]);
        }

        $user = MobileUser::where('email', $request->email)
            ->withTrashed()
            ->first();
        if ($user && Hash::check($request->password, $user->password)) {
            if ($user->deleted_at) {
                return response()->json([
                    'error' => true,
                    'message' => 'Account Closed!',
                    'status' =>'closed'
                ], 401);
            }

            if (!$user->status) {
                return response()->json([
                    'error' => true,
                    'message' => 'User is deactivated!',
                    'status' =>'deactivated',
                ], 401);
            }

            if ($user->banned) {
                $banned_timestamp = json_decode($user->banned_timestamp);
                if (isset($banned_timestamp) && Carbon::parse($banned_timestamp->end_date) > Carbon::now()) {
                    return response()->json([
                        'error' => true,
                        'message' => 'Account is banned until ' . Carbon::parse($banned_timestamp->end_date)->format('Y-m-d') . '! You can contact our support team at <a href="mailto:<EMAIL>"><EMAIL></a>',
                        'status' => 'suspended',
                    ], 401);
                } else {
                    $user->banned = false;
                    $user->banned_timestamp = null;
                    $user->save();
                }
            }

            // if (!$user->email_verified) {
            //     return response()->json([
            //         'error' => true,
            //         'message' => 'Plese verify your email to login!',
            //         'status' => 'verify',
            //     ]);
            // }

            $user->update(['firebase_token' => $request->firebase_token,
                'platform' => $request->platform, 'login_type' => $request->login_type,
                'app_version' => $request->app_version]);

            $token = $user->createToken('Scegli_lora')->accessToken;

            $userAllData = MobileUser::with(['employments', 'qualifications', 'siaCertificate', 'languages'])->find($user->id);

            return response()->json([
                'error' => false,
                'message' => 'Success',
                'token' => $token,
                'data' => new ProfileResource($userAllData),
                'session' => [
                    'expires_in' => config('session.lifetime') * 60, // Convert minutes to seconds
                    'warning_threshold' => 300 // 5 minutes warning
                ]
            ]);
        }

        return response()->json([
            'error' => true,
            'message' => 'Unauthorized access',
            'token' => '',
            'data' => (object)array(),
            'status' => 'unauthorized',
        ], 401);
    }

    public function freelancer_profile(FreelancerProfileRequest $request)
    {
        $user = Auth::guard('api')->user();

        $user->update($request->all());
        return response()->json([
            'error' => false,
            'message' => 'Success',
            'data' => $this->userObj($user),
        ]);
    }

    public function registerVerification(RegisterVerificationRequest $request): JsonResponse
{
    // Generate a unique request ID for traceability
    $requestId = uniqid('submission_', true);
    
    // Log the start of the verification process
    $requestData = $request->all();
    // Remove base64 image data from log to avoid excessive log size
    $logData = $requestData;
    foreach (self::REGISTRATION_DOCUMENTS as $document) {
        if (isset($logData[$document])) {
            $logData[$document] = '[BASE64_IMAGE_DATA_REMOVED]';
        }
    }
    
    \Log::channel('submissions')->info('Operator profile verification submission started', [
        'user_id' => auth()->id(),
        'request_id' => $requestId,
        'request_data' => $logData
    ]);
    
    $data = $request->except(self::REGISTRATION_DOCUMENTS);
    $user = Auth::guard('api')->user();
    if (!$user) {
        \Log::channel('submissions')->error('Operator profile verification submission failed - Unauthorized', [
            'user_id' => 'unknown',
            'request_id' => $requestId,
            'error' => 'Unauthorized access'
        ]);
        return response()->json(['error' => true, 'message' => 'Unauthorized'], 401);
    }

    $siaLicenceNumber = $data['sia_licence_number'] ?? '';
    $used = MobileUser::where('sia_licence_number', $siaLicenceNumber)->first();
    
    if ($used) {
        \Log::channel('submissions')->warning('Operator profile verification submission failed - SIA license already used', [
            'user_id' => auth()->id(),
            'request_id' => $requestId,
            'sia_licence_number' => $siaLicenceNumber
        ]);
        return response()->json([
            'error' => true,
            'message' => 'Sia Licence has already been used in Surely!',
        ]);
    }

    // Track which documents were submitted
    $submittedDocuments = [];
    
    foreach (self::REGISTRATION_DOCUMENTS as $document) {
        if ($request->has($document)) {
            $data[$document] = $this->base64Upload($document, $request->get($document));
            $submittedDocuments[$document] = 'Submitted';
        } else {
            $submittedDocuments[$document] = 'Not submitted';
        }
    }

    $siaCertificate = SiaCertificate::where('user_id', auth()->id())->first();
    
    if (!$siaCertificate) {
        $siaCertificate = SiaCertificate::create([
            'user_id' => auth()->id(),
        ]);
    }

    //ToDo: Verification of documents to be done from Admin Panel

    // if ($data['id_front_document'] && $request->get('document_type') == 'passport') {
    //     $siaCertificate->id_check = true;
    // } elseif ($data['id_front_document'] && $data['id_back_document']) {
    //     $siaCertificate->id_check = true;
    // }

    if ($data['sia_licence_card_photo']) {
        $siaCertificate->sia_licence = true;
    }

    //ToDo: Verification of documents to be done from Admin Panel

    // if ($data['address_verification_document']) {
    //     $siaCertificate->proof_of_address = true;
    // }
    
    $siaCertificate->save();

    if(! $user->update($data)) {
        \Log::channel('submissions')->error('Operator profile verification submission failed - Database update error', [
            'user_id' => auth()->id(),
            'request_id' => $requestId
        ]);
        return response()->json([
            'error' => true,
            'message' => 'Data cannot be updated!',
            'data' => $user
        ]);
    }

    // Initialize notification status
    $userNotificationStatus = 'not_attempted';
    $adminNotificationStatus = 'not_attempted';

    // Send notification to user
    try {
        \Log::channel('submissions')->info('Attempting to send document verification notification to user', [
            'user_id' => $user->id,
            'request_id' => $requestId
        ]);
        
        $user->notify(new DocumentVerificationNotification($user->name));
        
        \Log::channel('submissions')->info('Successfully sent document verification notification to user', [
            'user_id' => $user->id,
            'request_id' => $requestId
        ]);
        
        $userNotificationStatus = 'success';
    } catch (Exception $e) {
        // Log notification error
        \Log::channel('submissions')->warning('Failed to send document verification notification to user', [
            'user_id' => $user->id,
            'request_id' => $requestId,
            'error' => $e->getMessage()
        ]);
        
        $userNotificationStatus = 'failed';
    }

    // Send notification to admin
    try {
        \Log::channel('submissions')->info('Attempting to send document submission notification to admin', [
            'user_id' => $user->id,
            'request_id' => $requestId
        ]);
        
        $adminUser = new \App\Models\AdminUser();
        $adminUser->notify(new \App\Notifications\AdminDocumentSubmissionNotification(
            $user->id,
            $user->name,
            $submittedDocuments
        ));
        
        \Log::channel('submissions')->info('Successfully sent document submission notification to admin', [
            'user_id' => $user->id,
            'request_id' => $requestId
        ]);
        
        $adminNotificationStatus = 'success';
    } catch (Exception $e) {
        // Log notification error
        \Log::channel('submissions')->warning('Failed to send document submission notification to admin', [
            'user_id' => $user->id,
            'request_id' => $requestId,
            'error' => $e->getMessage()
        ]);
        
        $adminNotificationStatus = 'failed';
    }

    \Log::channel('submissions')->info('Operator profile verification submission completed', [
        'user_id' => auth()->id(),
        'request_id' => $requestId,
        'result' => 'success',
        'user_notification_status' => $userNotificationStatus,
        'admin_notification_status' => $adminNotificationStatus
    ]);

    return response()->json([
        'error' => false,
        'message' => 'Data updated successfully!',
        'data' => $user
    ]);
}

    public function registerVerificationClient(Request $request): JsonResponse
    {
        // Generate a unique request ID for traceability
        $requestId = uniqid('client_submission_', true);
        
        // Log the start of the verification process
        $requestData = $request->all();
        // Remove sensitive data from logs
        $logData = $requestData;
        
        \Log::channel('submissions')->info('Client profile verification submission started', [
            'user_id' => auth()->id(),
            'request_id' => $requestId,
            'request_data' => $logData
        ]);
        
        $data = $request->all();
        $user = auth()->user();
        
        if ($user->account_type != MobileUser::business) {
            \Log::channel('submissions')->warning('Client profile verification submission failed - Unauthorized account type', [
                'user_id' => auth()->id(),
                'request_id' => $requestId,
                'account_type' => $user->account_type
            ]);
            
            return response()->json(['error' => true, 'message' => 'Unauthorized user!'], 401);
        }

        if(! $user->update($data)) {
            \Log::channel('submissions')->error('Client profile verification submission failed - Database update error', [
                'user_id' => auth()->id(),
                'request_id' => $requestId
            ]);
            
            return response()->json([
                'error' => true,
                'message' => 'Data cannot be updated!',
                'data' => $user
            ]);
        }
        
        // Initialize notification status
        $adminNotificationStatus = 'not_attempted';
        
        // Send notification to admin about client verification
        try {
            \Log::channel('submissions')->info('Attempting to send client verification notification to admin', [
                'user_id' => $user->id,
                'request_id' => $requestId
            ]);
            
            $adminUser = new \App\Models\AdminUser();
            $adminUser->notify(new \App\Notifications\AdminClientVerificationNotification(
                $user->id,
                $user->name,
                $user->company_name ?? 'Not provided'
            ));
            
            \Log::channel('submissions')->info('Successfully sent client verification notification to admin', [
                'user_id' => $user->id,
                'request_id' => $requestId
            ]);
            
            $adminNotificationStatus = 'success';
        } catch (Exception $e) {
            // Log notification error
            \Log::channel('submissions')->warning('Failed to send client verification notification to admin', [
                'user_id' => $user->id,
                'request_id' => $requestId,
                'error' => $e->getMessage()
            ]);
            
            $adminNotificationStatus = 'failed';
        }
        
        \Log::channel('submissions')->info('Client profile verification submission completed', [
            'user_id' => auth()->id(),
            'request_id' => $requestId,
            'result' => 'success',
            'admin_notification_status' => $adminNotificationStatus
        ]);

        return response()->json([
            'error' => false,
            'message' => 'Data updated successfully!',
            'data' => $user
        ]);
    }

    public function business_profile(BusinessProfileRequest $request)
    {
        $user = Auth::guard('api')->user();

        $user->update($request->all());
        return response()->json([
            'error' => false,
            'message' => 'Success',
            'data' => $this->userObj($user),
        ]);
    }

    public function change_password(PasswordRequest $request)
    {
        $user = Auth::guard('api')->user();
        $oldPassword = $request->old_password;
        $newPassword = $request->password;
        $hashedPassword = $user->password;

        if (Hash::check($oldPassword, $hashedPassword)) {
            $user->password = Hash::make($newPassword);
            $user->save();
        } else {
            return response()->json([
                'error' => true,
                'message' => 'Old password does not match.'
            ]);
        }
        return response()->json([
            'error' => false,
            'message' => 'Success',
        ]);
    }

    public function deleteAccount()
    {
        $user = MobileUser::find(auth()->id());
        
        if ($user->delete()) {
            try {
                $user->notify(new AccountDeletedNotification($user->name));
            } catch (Exception $e) {
                //
            }

            return response()->json([
                'error' => false,
                'message' => 'Account deleted successfully!'
            ]);
        } else {
            return response()->json([
                'error' => true,
                'message' => 'Account cannot be deleted!'
            ]);
        }
    }

    public function expertise_fields()
    {
        return response()->json([
            'error' => false,
            'message' => 'Success',
            'data' => ExpertiseField::select('id', 'name')->get(),
        ]);
    }

    public function gigs()
    {
        return response()->json([
            'error' => false,
            'message' => 'Success',
            'data' => Gig::select('id', 'name')->get(),
        ]);
    }

    //TODO: This method should not be used
    public function update_location($lat,$lng,$cityId)
    {
        Auth::guard('api')->user()->update(['lat'=>$lat,'lng'=>$lng, 'city_id'=>$cityId]);

        return response()->json([
            'error' => false,
            'message' => 'Success',
        ]);
    }

    public function cities()
    {
        $cities = City::select('id','name')->get();
        return response()->json([
            'error' => false,
            'message' => 'Success',
            'data' =>$cities
        ]);
    }

    //send reset password

    public function send_forgot_password(SendResetPasswordRequest $request){
        $user = MobileUser::where('email', $request->email)->first();

        if (!$user) {
            return response()->json(['error' => true, 'message' => 'Invalid email address']);
        }

        $random_code = rand(100000, 999999);
        $user->email_code = $random_code;
        $user->email_code_expires_at = now()->addMinutes(10);
        $user->save();

        try {
            $user->notify(new ResetPasswordNotification($random_code, $user->name));
        } catch (Exception $e) {
            \Log::error('Email verification error: ' . $e->getMessage());
            return response()->json([
                'error' => true,
                'message' => 'Error sending verification link to ' . $user->email,
            ]);
        }

        return response()->json(['error' => false, 'message' => 'Email sent to ' . $user->email]);
}

    public function confirm_forgot_code(ConfirmResetPasswordRequest $request){
            $user = MobileUser::where('email', $request->email)->first();

            if (!$user) {
                return response()->json(['error' => true, 'message' => 'Invalid email address']);
            }

            if($user->code != null){
                return response()->json(['error' => true, 'message' => 'Pleas send code to your email']);
            }

            if ($user->email_code != $request->code) {
                return response()->json(['error' => true, 'message' => 'Invalid code']);
            }

            if (now()->gt($user->email_code_expires_at)) {
                return response()->json(['error' => true, 'message' => 'Code expired']);
            }

            if($request->filled('unlink_socials') && $request->unlink_socials == true){
                $user->social_id = null;
            }

            $secret = 'surely_security';
            $data = [
                'email' => $user->email,
                'expires_at' => time() + 3600, // Expires in 1 hour,
            ];

            $token = JWT::encode($data, $secret, 'HS384');

            $user->email_token = $token;
            $user->save();

            return response()->json([
                'error' => false,
                'message' => 'Success',
                'verification_token' => $token,
            ]);
    }

    public function new_password(Request $request)
    {
        $validator = $request->validate(
            [
                'email' => 'required',
                'verification_token' => 'required',
                'password' => 'required|string|min:8|confirmed',
            ]
        );

        try {
            $secret = 'surely_security';
            $token = $validator['verification_token'];
            $data = JWT::decode($token, new Key($secret, 'HS384'));
            
            $user = MobileUser::where('email', $validator['email'])->first();
            
            if ($user->email_token != $token) {
                return response()->json([
                    'error' => true,
                    'message' => 'Invalid Request!',
                ], 401);
            }
           
            if ($data->expires_at < Carbon::now()->timestamp) {
                return response()->json([
                    'error' => true,
                    'message' => 'Unauthenticated user! Token has expired!',
                ], 401);
            }

            $token = $user->createToken('5at')->accessToken;
            $user->email_token = null;
            $user->email_verified = true;
            $user->password = bcrypt($validator['password']);
            $user->save();

            $response = [
                'error' => false,
                'message' => 'Success',
                'token' => $token,
                'data' => $this->userObj($user)
            ];

            return response()->json($response);
        } catch(ErrorException $e) {
            return response()->json([
                'error' => true,
                'message' => 'Unauthenticated user! Invalid token!' . $e,
            ], 401);
        } catch(\Firebase\JWT\ExpiredException $e) {
            return response()->json([
                'error' => true,
                'message' => 'Token has expired!',
            ], 401);
        } catch (\Firebase\JWT\SignatureInvalidException $e){
            return response()->json([
                'error' => true,
                'message' => 'Invalid key to decrypt token!',
            ], 401);
        }
    }

    public function upgradeToClient(Request $request)
    {
        $user = auth()->user();
        
        if ($user->account_type !== MobileUser::guest) {
            return response()->json([
                'error' => true,
                'message' => 'Only guest accounts can be upgraded to client accounts'
            ], 400);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'postal_code' => 'required|string',
            'phone' => 'required|string',
            'address' => 'required|string',
            'address_1' => 'required|string', 
            'address_2' => 'nullable|string', 
            'location_range' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => true,
                'message' => 'Please fill in all required fields',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();
            
            // Update user details
            $user->update([
                'account_type' => MobileUser::business, // Change to client type
                'name' => $request->name,
                'phone' => $request->phone,
                'postal_code' => $request->postal_code,
                'address' => $request->address,
                'address_1' => $request->address_1,
                'address_2' => $request->address_2,
                'location_range' => $request->location_range,
                'is_guest' => false
            ]);

            DB::commit();

            return response()->json([
                'error' => false,
                'message' => 'Account successfully upgraded to client',
                'data' => $this->userObj($user)
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'error' => true,
                'message' => 'Failed to upgrade account'
            ], 500);
        }
    }
}
