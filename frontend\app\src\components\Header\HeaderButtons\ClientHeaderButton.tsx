// @ts-nocheck
import { useState, useContext, useEffect } from 'react';
import { Button, DropdownMenu, Text, View, Avatar, useToggle } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import { AuthContext } from 'src/context/AuthContext';
import { AppContext } from 'src/context/AppContext';

import clientdropdownicon1 from '../../../assets/icons/clientdropdownicon/clientdropdownicon1.svg';
import clientdropdownicon2 from '../../../assets/icons/clientdropdownicon/clientdropdownicon2.svg';
import clientdropdownicon3 from '../../../assets/icons/clientdropdownicon/clientdropdownicon3.svg';
import clientdropdownicon4 from '../../../assets/icons/clientdropdownicon/clientdropdownicon4.svg';
import clientdropdownicon5 from '../../../assets/icons/clientdropdownicon/clientdropdownicon5.svg';
import clientdropdownicon6 from '../../../assets/icons/clientdropdownicon/clientdropdownicon6.svg';
import clientdropdownicon7 from '../../../assets/icons/clientdropdownicon/clientdropdownicon7.svg';
import clientdropdownicon8 from '../../../assets/icons/clientdropdownicon/clientdropdownicon8.svg';
import operatordropdown5 from '../../../assets/icons/operatordropdownicon/operatordropdown5.svg';

const ClientHeaderButton = () => {
  const navigate = useNavigate();
  const { active, activate, deactivate } = useToggle()

  const { user, unAuthenticateUser } = useContext(AuthContext);

  const name = user?.name ? user?.name : user?.profile?.name

  const lastname = name?.split(' ').pop()?.charAt(0).toUpperCase() + name?.split(' ').pop()?.slice(1);
  const firstname = name?.split(' ').shift()?.charAt(0).toUpperCase() + name?.split(' ').shift()?.slice(1);
  const initialName = `${firstname || ''} ${lastname || ''}`;
  const initialNameFirstCharAt = (firstname?.[0] || '') + (lastname?.[0] || '');

  const [windowWidth, setWindowWidth] = useState(window.innerWidth);

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const isSmallScreen = windowWidth <= 1024;

  return (
    <DropdownMenu onOpen={activate} onClose={deactivate} contentGap={6} position='bottom'>
      <DropdownMenu.Trigger>
        {(attributes) => (
          <Button type='submit' rounded={true} className='!bg-[#323c58] h-[auto]' attributes={attributes}>
            <View className='flex flex-row justify-center items-center gap-2'>
              <Avatar
                size={8}
                initials={initialNameFirstCharAt}
                color='neutral'
                variant='faded'
                className='rubik font-medium text-[14px] leading-[20px] text-[#323C58]'
              ></Avatar>
              {!isSmallScreen &&<Text className='rubik font-medium text-[15px] leading-[20px] text-[#FFFFFF]'>{initialName}</Text>}
              <span className='material-icons-outlined text-white'>{active ? 'close' : 'menu'}</span>
            </View>
          </Button>
        )}
      </DropdownMenu.Trigger>
      <DropdownMenu.Content>
        <DropdownMenu.Section>
          <DropdownMenu.Item  onClick={() => navigate('/client-dashboard?refresh=true')}>
            <Text className='flex items-center gap-3 rubik font-[14px] leading-[20px] font-medium'>
              <img src={clientdropdownicon1}  />
              Dashboard
            </Text>
          </DropdownMenu.Item>
          <DropdownMenu.Item onClick={() => navigate('/chat')}>
            <Text className='flex items-center gap-3 rubik font-[14px] leading-[20px] font-medium'>
              <img src={clientdropdownicon2} />
              Messages
            </Text>
          </DropdownMenu.Item>
          <DropdownMenu.Item onClick={() => navigate('/search-operator')}>
            <Text className='flex items-center gap-3 rubik font-[14px] leading-[20px] font-medium whitespace-nowrap'>
              <img src={clientdropdownicon3} />
              Find Security Operatives
            </Text>
          </DropdownMenu.Item>
          <DropdownMenu.Item onClick={() => navigate('/post-job')}>
            <Text className='flex items-center gap-3 rubik font-[14px] leading-[20px] font-medium'>
              <img src={clientdropdownicon4} />
              Post a job
            </Text>
          </DropdownMenu.Item>
          <DropdownMenu.Item onClick={() => navigate('/manage-jobs')}>
            <Text className='flex items-center gap-3 rubik font-[14px] leading-[20px] font-medium'>
              <img src={clientdropdownicon5} />
              {/* <span className='material-icons text-base text-[#323C58]'>work</span> */}
              Manage your jobs
            </Text>
          </DropdownMenu.Item>
          <DropdownMenu.Item onClick={() => navigate('/favorite-operator')}>
            <Text className='flex items-center gap-3 rubik font-[14px] leading-[20px] font-medium'>
              {/* <img src={clientdropdownicon6} /> */}
              <span className='material-icons-outlined text-base leading-[16px] text-[#323C58]'>favorite</span>
              Favourites list
            </Text>
          </DropdownMenu.Item>
          <DropdownMenu.Item onClick={() => navigate('/client-settings-notification')}>
            <Text className='flex items-center gap-3 rubik font-[14px] leading-[20px] font-medium'>
              {/* <img src={operatordropdown5} /> */}
              <span className='material-icons-outlined text-base leading-[16px] text-[#323C58]'>visibility</span>
              Notifications
            </Text>
          </DropdownMenu.Item>
          <DropdownMenu.Item onClick={() => navigate('/client-settings')}>
            <Text className='flex items-center gap-3 rubik font-[14px] leading-[20px] font-medium'>
              {/* <img src={clientdropdownicon7} /> */}
              <span className='material-icons-outlined text-base leading-[16px] text-[#323C58]'>settings</span>
              Account settings
            </Text>
          </DropdownMenu.Item>
        </DropdownMenu.Section>
        <DropdownMenu.Section>
          <DropdownMenu.Item
            onClick={() => {
              unAuthenticateUser();
              navigate('/');
            }}
          >
            <Text className='flex items-center gap-2 rubik font-[14px] leading-[20px] font-medium'>
              <img src={clientdropdownicon8} />
              Log out
            </Text>
          </DropdownMenu.Item>
        </DropdownMenu.Section>
      </DropdownMenu.Content>
    </DropdownMenu>
  );
};

export default ClientHeaderButton;
