import React, { useRef, useState } from 'react';
import { labelStyles } from '../../JobPostIntro/styles';

interface FileUploadTwoProps {
  label: string;
  onChange: (files: File[]) => void;
  accept?: string;
  multiple?: boolean;
  maxFiles?: number;
}

const FileUploadTwo: React.FC<FileUploadTwoProps> = ({
  label,
  onChange,
  accept = 'image/*',
  multiple = true,
  maxFiles = 3
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);

  const handleClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const newFiles = Array.from(files);
      const combinedFiles = [...selectedFiles, ...newFiles];
      
      // Limit the number of files
      const limitedFiles = combinedFiles.slice(0, maxFiles);
      
      setSelectedFiles(limitedFiles);
      onChange(limitedFiles);
      
      // Reset the input value to allow selecting the same file again
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleRemoveFile = (indexToRemove: number) => {
    const updatedFiles = selectedFiles.filter((_, index) => index !== indexToRemove);
    setSelectedFiles(updatedFiles);
    onChange(updatedFiles);
  };

  const containerStyles: React.CSSProperties = {
    marginBottom: '24px',
  };

  const uploadAreaStyles: React.CSSProperties = {
    border: '2px dashed #ddd',
    borderRadius: '8px',
    padding: '16px',
    textAlign: 'center',
    cursor: 'pointer',
    marginTop: '8px',
    backgroundColor: '#f9f9f9',
    transition: 'all 0.3s ease'
  };

  const fileListStyles: React.CSSProperties = {
    marginTop: '12px',
    fontSize: '14px',
  };

  const fileItemStyles: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '8px 12px',
    backgroundColor: '#f8f9fa',
    borderRadius: '6px',
    marginBottom: '8px',
    border: '1px solid #e9ecef'
  };

  const deleteButtonStyles: React.CSSProperties = {
    backgroundColor: '#dc3545',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    padding: '4px 8px',
    fontSize: '12px',
    cursor: 'pointer',
    fontWeight: 500
  };

  const fileCountStyles: React.CSSProperties = {
    fontSize: '12px',
    color: '#6c757d',
    marginTop: '4px'
  };

  return (
    <div style={containerStyles}>
      <label style={labelStyles}>{label}</label>
      <div 
        style={{
          ...uploadAreaStyles,
          opacity: selectedFiles.length >= maxFiles ? 0.6 : 1,
          cursor: selectedFiles.length >= maxFiles ? 'not-allowed' : 'pointer'
        }} 
        onClick={selectedFiles.length < maxFiles ? handleClick : undefined}
        onMouseOver={(e) => {
          if (selectedFiles.length < maxFiles) {
            (e.currentTarget as HTMLDivElement).style.borderColor = '#007bff';
            (e.currentTarget as HTMLDivElement).style.backgroundColor = '#f0f8ff';
          }
        }}
        onMouseOut={(e) => {
          if (selectedFiles.length < maxFiles) {
            (e.currentTarget as HTMLDivElement).style.borderColor = '#ddd';
            (e.currentTarget as HTMLDivElement).style.backgroundColor = '#f9f9f9';
          }
        }}
      >
        <p>
          {selectedFiles.length >= maxFiles 
            ? `Maximum ${maxFiles} files reached`
            : `Click to upload ${multiple ? 'files' : 'a file'}`
          }
        </p>
        <div style={fileCountStyles}>
          {selectedFiles.length}/{maxFiles} files selected
        </div>
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          style={{ display: 'none' }}
          accept={accept}
          multiple={multiple}
        />
      </div>
      {selectedFiles.length > 0 && (
        <div style={fileListStyles}>
          <p style={{ marginBottom: '8px', fontWeight: 500 }}>Selected files:</p>
          {selectedFiles.map((file, index) => (
            <div key={index} style={fileItemStyles}>
              <span style={{ flex: 1, marginRight: '12px' }}>{file.name}</span>
              <button 
                type="button"
                style={deleteButtonStyles}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleRemoveFile(index);
                }}
                onMouseOver={(e) => {
                  (e.currentTarget as HTMLButtonElement).style.backgroundColor = '#c82333';
                }}
                onMouseOut={(e) => {
                  (e.currentTarget as HTMLButtonElement).style.backgroundColor = '#dc3545';
                }}
              >
                Remove
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default FileUploadTwo;
