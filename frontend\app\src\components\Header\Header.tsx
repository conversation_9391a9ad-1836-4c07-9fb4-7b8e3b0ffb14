// @ts-nocheck
import { useContext, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import HeaderMenu from './HeaderMenu/HeaderMenu';
import { AuthContext } from 'src/context/AuthContext';
import LoginButton from './HeaderButtons/LoginHeaderButton';
import SignUpButton from './HeaderButtons/SignUpHeaderButton';
import ClientButton from './HeaderButtons/ClientHeaderButton';
import OperatorButton from './HeaderButtons/OperatorHeaderButton';
import GuestHeaderButton from './HeaderButtons/GuestHeaderButton';
import '../../components/Header/HeaderMenu/HeaderMenu.css';
import SurelyHeaderIcon from './SurelyHeaderIcon/SurelyHeaderIcon';
import SmallScreenLoginSignupMenu from './HeaderButtons/SmallScreenLoginSignupMenu';
import { useAuthContext } from '../../context/AuthContext';

const Header = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useContext(AuthContext);
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const { isAuthenticated: authContextIsAuthenticated, user: authContextUser } = useAuthContext();

  const accountType = user?.profile?.account_type ? user?.profile?.account_type : user?.account_type;

  // Check if user is guest (similar to GuestGuard logic)
  const isGuest = user?.profile ? user?.profile?.account_type === '0' : user?.account_type === '0';

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const isSmallScreen = windowWidth <= 1024;

  const handleNavigation = () => {
    if (isAuthenticated) {
      if (accountType == 1) {
        navigate('/search-jobs');
      } else if (accountType == 2) {
        navigate('/client-dashboard?refresh=true');
      } else if (accountType == 5) {
        navigate('/search-operator');
      }
    } else {
      navigate('/');
    }
  };

  return (
    <header className='sticky top-0 z-[100] flex w-full bg-[#fefefe] p-[10px] shadow-md backdrop-blur-[15px]'>
      <div className='mx-auto flex h-auto min-h-16 w-full max-w-[1320px] items-center justify-between'>
        <div className='flex flex-1 items-center'>
          <button onClick={handleNavigation} className='btn-no-hover mx-[0px]'>
            <SurelyHeaderIcon />
          </button>
          {!isSmallScreen && !isAuthenticated && <HeaderMenu />}
        </div>
        {!isAuthenticated ? (
          !isSmallScreen ? (
            <div className='mr-[0px] flex w-full max-w-[235px] items-center justify-between sm:mr-[0px] sm:max-w-[232px]'>
              <LoginButton />
              <SignUpButton />
            </div>
          ) : (
            <SmallScreenLoginSignupMenu />
          )
        ) : accountType == 2 ? (
          <ClientButton />
        ) : accountType == 1 ? (
          <OperatorButton />
        ) : accountType == 5 ? (
          <GuestHeaderButton />
        ) : null}
      </div>

      
    </header>
  );
};

export default Header;
