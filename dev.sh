#!/bin/bash
# dev.sh - One-command local dev launcher for Surely
# Usage: ./dev.sh
#
# This script starts the Laravel backend and the frontend dev server.
# It runs them in parallel and shows combined logs with prefixes.
# Press Ctrl+C to stop both.

set -euo pipefail

# Resolve repo root to this script's directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" >/dev/null 2>&1 && pwd)"
cd "$SCRIPT_DIR"

# Colors
RED=$(printf '\033[31m')
GREEN=$(printf '\033[32m')
YELLOW=$(printf '\033[33m')
BLUE=$(printf '\033[34m')
BOLD=$(printf '\033[1m')
RESET=$(printf '\033[0m')

log() { echo -e "${BOLD}${BLUE}[dev]${RESET} $*"; }
ok()  { echo -e "${BOLD}${GREEN}[ok]${RESET}  $*"; }
warn(){ echo -e "${BOLD}${YELLOW}[warn]${RESET} $*"; }
err() { echo -e "${BOLD}${RED}[err]${RESET}  $*" 1>&2; }

# Ensure cleanup on exit
PIDS=()
cleanup() {
  warn "Stopping dev processes..."
  for pid in "${PIDS[@]:-}"; do
    if kill -0 "$pid" 2>/dev/null; then
      kill "$pid" 2>/dev/null || true
      wait "$pid" 2>/dev/null || true
    fi
  done
  ok "All processes stopped."
}
trap cleanup EXIT INT TERM

# Function to run a command in a sub-shell with prefixed output
run_prefixed() {
  local name="$1"; shift
  (
    # shellcheck disable=SC2155
    local COLOR=$(printf '\033[36m')
    case "$name" in
      backend) COLOR=$(printf '\033[35m');;
      frontend) COLOR=$(printf '\033[36m');;
    esac
    set -o pipefail
    "$@" 2>&1 | while IFS= read -r line; do
      echo -e "${COLOR}[${name}]${RESET} $line"
    done
  ) &
  PIDS+=($!)
}

# Checks
if ! command -v php >/dev/null 2>&1; then
  err "php not found in PATH. Please install PHP or add it to PATH."
  exit 1
fi
if ! command -v npm >/dev/null 2>&1; then
  err "npm not found in PATH. Please install Node.js/npm."
  exit 1
fi

# Start backend
if [ -d "surely_backend/app-files" ]; then
  log "Starting Laravel backend (php artisan serve) ..."
  run_prefixed backend bash -lc 'cd "surely_backend/app-files" && php artisan serve'
else
  err "Directory surely_backend/app-files not found."
  exit 1
fi

# Start frontend
if [ -d "frontend/app" ]; then
  log "Starting frontend (npm run dev) ..."
  run_prefixed frontend bash -lc 'cd "frontend/app" && npm run dev'
else
  err "Directory frontend/app not found."
  exit 1
fi

ok "Both servers launched. Press Ctrl+C to stop."
# Wait forever until interrupted
wait