import React from 'react';
import { labelStyles } from '../styles';

// The props for the component are updated for full functionality.
interface CounterInputProps {
  label: string;
  value: number | string;
  onIncrement: () => void;
  onDecrement: () => void;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

const CounterInput: React.FC<CounterInputProps> = ({ 
  label, 
  value, 
  onIncrement, 
  onDecrement,
  onChange
}) => {
  return (
    <div>
      {/* Label with styling from the parent form */}
      <label style={labelStyles}>
        {label}
      </label>

      {/* Container for the counter itself */}
      <div className="flex items-center space-x-2 mb-6">
        <div className="flex items-center bg-white border border-gray-400 rounded-full shadow-sm">
          {/* Decrement Button */}
          <button
            type="button"
            onClick={onDecrement}
            className="w-10 h-10 flex items-center justify-center text-xl font-bold text-black bg-white hover:bg-gray-100 rounded-l-full"
          >
            -
          </button>

          {/* Input Field for direct typing */}
          <input
            type="text"
            value={value}
            onChange={onChange}
            className="px-2 py-2 text-xl font-normal text-black text-center bg-white border-none outline-none"
            style={{ width: '40px', minWidth: '40px' }}
          />

          {/* Increment Button */}
          <button
            type="button"
            onClick={onIncrement}
            className="w-10 h-10 flex items-center justify-center text-xl font-bold text-black bg-white hover:bg-gray-100 rounded-r-full"
          >
            +
          </button>
        </div>
      </div>
    </div>
  );
};

export default CounterInput;