<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TelemetryController extends Controller
{
    /**
     * Handle client-side submission attempt telemetry
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function logSubmissionAttempt(Request $request)
    {
        $data = $request->all();
        $event = $data['event'] ?? 'submission_attempt';
        $telemetryData = $data['data'] ?? [];
        
        // Add request metadata
        $telemetryData['ip'] = $request->ip();
        $telemetryData['user_agent'] = $request->header('User-Agent');
        $telemetryData['server_timestamp'] = now()->toISOString();
        
        // Log the telemetry data
        Log::channel('submissions')->info("Client telemetry: {$event}", $telemetryData);
        
        return response()->json(['status' => 'success']);
    }
    
    /**
     * Handle client-side submission error telemetry
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function logSubmissionError(Request $request)
    {
        $data = $request->all();
        $event = $data['event'] ?? 'submission_error';
        $telemetryData = $data['data'] ?? [];
        
        // Add request metadata
        $telemetryData['ip'] = $request->ip();
        $telemetryData['user_agent'] = $request->header('User-Agent');
        $telemetryData['server_timestamp'] = now()->toISOString();
        
        // Log the telemetry data
        Log::channel('submissions')->warning("Client error: {$event}", $telemetryData);
        
        return response()->json(['status' => 'success']);
    }
    
    /**
     * Handle client-side UI error telemetry
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function logUiError(Request $request)
    {
        $data = $request->all();
        $event = $data['event'] ?? 'ui_error';
        $telemetryData = $data['data'] ?? [];
        
        // Add request metadata
        $telemetryData['ip'] = $request->ip();
        $telemetryData['user_agent'] = $request->header('User-Agent');
        $telemetryData['server_timestamp'] = now()->toISOString();
        
        // Log the telemetry data with appropriate level based on error type
        if (isset($telemetryData['errorType']) && in_array($telemetryData['errorType'], ['network_error', 'timeout_error', 'offline_error'])) {
            // Network-related errors are warnings, not critical errors
            Log::channel('submissions')->warning("Client UI error: {$event}", $telemetryData);
        } else {
            // Other UI errors are errors
            Log::channel('submissions')->error("Client UI error: {$event}", $telemetryData);
        }
        
        return response()->json(['status' => 'success']);
    }
}
