// @ts-nocheck
import React, { useState } from 'react';
import { But<PERSON>, Text } from 'reshaped';
import { useToastSystem } from 'src/context/ToastSystemContext';
import { addPostJob } from 'src/services/jobs';
import { useNavigate } from 'react-router-dom';
import { Image } from 'reshaped';
import surleyicon from '../../../assets/icons/surleyicon/surleyicon.png';

// Import dedicated components for JobPostIntroTwo
import TitleHeaderTwo from './components/TitleHeaderTwo';
import SubtitleTextTwo from './components/SubtitleTextTwo';
import HeaderLogoTwo from './components/HeaderLogoTwo';
import LogoIconTwo from './components/LogoIconTwo';
import StepperDotsTwo from './components/StepperDotsTwo';
import FormInputTwo from './components/FormInputTwo';
import FormSelectTwo from './components/FormSelectTwo';
import FormTextAreaTwo from './components/FormTextAreaTwo';
import FileUploadTwo from './components/FileUploadTwo';
import ToggleSwitchTwo from './components/ToggleSwitchTwo';
import PrimaryButtonTwo from './components/PrimaryButtonTwo';
import SecondaryButtonTwo from './components/SecondaryButtonTwo';

// Import styles
import {
  containerStyles,
  headerStyles,
  mainLayoutStyles,
  leftColumnStyles,
  rightColumnStyles,
  formContainerStyles,
  formHeadingStyles,
  centerContentStyles,
  footerStyles,
  actionContainerStyles,
  finalStepTextStyles
} from '../JobPostIntro/styles';

// Import assets
import caretDown from '../../../assets/images/job-post-intro/caret-down.svg';
import surelyLogo from '../../../assets/images/job-post-intro/surely-logo.png';
import usersGroupIcon from '../../../assets/images/job-post-intro/User _ Users_Group.svg';

const JobPostIntroTwo: React.FC = () => {
  const navigate = useNavigate();
  const toastSystem = useToastSystem();
  
  // Form state
  const [extraSkills, setExtraSkills] = useState('');
  const [inclusivityPledge, setInclusivityPledge] = useState(false);
  const [gigImages, setGigImages] = useState<File[]>([]);
  const [gigImagesBase64, setGigImagesBase64] = useState<(string | null)[]>([]);
  const [qualifications, setQualifications] = useState<string[]>([]);
  const [specialRequirements, setSpecialRequirements] = useState('');
  const [imageSizeError, setImageSizeError] = useState<string | null>(null);
  const [qualificationsMissing, setQualificationsMissing] = useState('');
  const [isButtonDisabled, setIsButtonDisabled] = useState(false);

  // Extra skills options (matching PostJob SurelyPro badge options)
  const skillsOptions = [
    { value: 'Customer Service', label: 'Customer Service' },
    { value: 'Use Of Equipment', label: 'Use of Equipment' },
    { value: 'Disability Focus', label: 'Disability Focus' },
    { value: 'Substance Awareness', label: 'Substance Awareness' },
    { value: 'Vulnerable People', label: 'Vulnerable People' },
    { value: 'Conflict Managament', label: 'Conflict Managament' },
  ];

  // Qualification options (matching PostJob)
  const qualificationOptions = [
    { value: 'Dog Handling Skills', label: 'Dog Handling Skills' },
    { value: 'Driving Skills', label: 'Driving Skills' },
    { value: 'Fire Safety', label: 'Fire Safety' },
    { value: 'First Aid', label: 'First Aid' },
    { value: 'Health & Safety', label: 'Health & Safety' },
    { value: 'Mechanical Restraints', label: 'Mechanical Restraints' },
    { value: 'Mental Health', label: 'Mental Health' },
    { value: 'Counter Terrorism', label: 'Counter Terrorism' },
    { value: 'Other', label: 'Other' },
  ];

  // Qualification handler functions (matching PostJob)
  const handleRemoveQualification = (qualification: string) => {
    setQualifications((prevQualifications) => prevQualifications.filter((selectedQualification) => selectedQualification !== qualification));
  };

  const clearAllSelectedQualifications = () => {
    setQualifications([]);
  };

  // Handle image file conversion to base64 (similar to PostJob.tsx)
  const handleImageFilesChange = (files: File[]) => {
    setImageSizeError(null);
    const base64Images: (string | null)[] = [];
    
    if (files.length === 0) {
      setGigImagesBase64([]);
      return;
    }

    files.forEach((file, index) => {
      // Check file size (5MB limit like PostJob.tsx)
      if (file.size > 5 * 1024 * 1024) {
        setImageSizeError('Image size exceeds the limit of 5MB. Please choose a smaller image.');
        toast.show({
          title: 'Error',
          text: 'Image size exceeds the limit of 5MB. Please choose a smaller image.',
        });
        return;
      }

      const reader = new FileReader();
      reader.onloadend = () => {
        base64Images[index] = reader.result as string;
        
        // Update state when all files are processed
        if (base64Images.filter(img => img !== null).length === files.length) {
          setGigImagesBase64(base64Images);
        }
      };
      reader.readAsDataURL(file);
    });
  };

  const submitJobToBackend = () => {
    // Get the job data from localStorage (from JobPostIntro)
    const jobData = JSON.parse(localStorage.getItem('jobPostData') || '{}');
    
    // Create the job post data object matching PostJob's structure
    const postJobData = {
      images: gigImagesBase64.filter(img => img !== null), // Include base64 images
      jobPostName: `${jobData.securityType} - ${jobData.location}`,
      selectedDates: jobData.selectedDates,
      siaLicence: jobData.securityType,
      industrySectors: [], // Not collected in JobPostIntro flow
      qualifications: qualifications,
      surelyProBadge: extraSkills ? [extraSkills] : [], // Map extraSkills to surelyProBadge array
      ratingLevel: 0, // Default rating
      hourlyRateMin: parseFloat(jobData.rate) || 0,
      hourlyRateMax: parseFloat(jobData.rate) || 0,
      jobTitle: `${jobData.securityType} Required`,
      numOperatives: parseInt(jobData.operatives) || 1,
      location: jobData.location,
      jobDescription: `We need ${jobData.operatives} ${jobData.securityType.toLowerCase()} operative${jobData.operatives > 1 ? 's' : ''} for a job in ${jobData.location}.`,
      dutyOfCare: specialRequirements, // Map special requirements to duty of care
      jobBenefits: '', // Not collected in JobPostIntro flow
      isEmergencyHire: false, // Default to false
      isInclusivityPledge: inclusivityPledge,
      payment: 10, // Default payment
    };

    console.log('Submitting job data to backend:', postJobData);
    
    // Use the same addPostJob service as PostJob component
    addPostJob(postJobData)
      .then((data) => {
        if (data.error) {
          toastSystem.showError('Error', data.message || 'An error occurred');
        } else {
          // Clear localStorage
          localStorage.removeItem('jobPostData');
          localStorage.removeItem('completeJobPostData');

          toastSystem.showSuccess('Congratulations!', 'Your Job Post is Now Live');
          navigate('/job-post-intro-three');
        }
      })
      .catch((error) => {
        toastSystem.showError('Error', error.message);
      });
  };

  const handleGetMatches = () => {
    if (!isButtonDisabled) {
      submitJobToBackend();
      setTimeout(() => {
        setIsButtonDisabled(false);
      }, 1000);
      setIsButtonDisabled(true);
    }
  };

  const handleBack = () => {
    navigate('/job-post-intro');
  };

  return (
    <div style={containerStyles}>
      {/* Header */}
      <header style={headerStyles}>
        <HeaderLogoTwo />
      </header>

      {/* Main Layout */}
      <div style={mainLayoutStyles}>
        {/* Left Panel */}
        <div style={leftColumnStyles}>
          <div style={centerContentStyles}>
            <LogoIconTwo src={surelyLogo} alt="Surely Logo" />
            <TitleHeaderTwo>Seconds away from your matches.</TitleHeaderTwo>
            <SubtitleTextTwo>That's it after this — easy, right?</SubtitleTextTwo>
            <StepperDotsTwo totalSteps={2} currentStep={2} />
          </div>
        </div>

        {/* Right Panel */}
        <div style={rightColumnStyles}>
          <form style={formContainerStyles}>
            {/* Optional fields disclaimer */}
            <div style={{ 
              marginBottom: '24px', 
              fontSize: '14px', 
              color: '#666',
              fontStyle: 'italic'
            }}>
              *These fields are optional, but the information helps get the best match.
            </div>

            {/* Extra skills */}
            <FormSelectTwo
              label="Need extra skills?"
              value={extraSkills}
              onChange={(e) => setExtraSkills(e.target.value)}
              options={skillsOptions}
              placeholder="Choose a SurelyPro skill"
              caretIcon={caretDown}
            />

            {/* Inclusivity pledge */}
            <div style={{
              fontFamily: 'Rubik',
              fontWeight: 500,
              fontStyle: 'Medium',
              fontSize: '20px',
              lineHeight: '100%',
              letterSpacing: '0%',
              color: '#374151',
              marginBottom: '15px',
              display: 'block'
            }}>
              Care about respectful, inclusive security?
            </div>
            <div style={{ marginBottom: '16px' }}>
              <div style={{ 
                display: 'flex', 
                alignItems: 'center',
                padding: '8px 0'
              }}>
                <img 
                  src={usersGroupIcon} 
                  alt="Users group" 
                  style={{ 
                    width: '24px', 
                    height: '24px',
                    marginRight: '12px'
                  }} 
                />
                <span style={{ 
                  color: 'rgba(26, 78, 141, 1)', 
                  fontSize: '14px',
                  fontWeight: 500,
                  marginRight: '12px',
                  flex: 1
                }}>
                  Inclusivity Pledge
                </span>
                <ToggleSwitchTwo
                  isChecked={inclusivityPledge}
                  onChange={setInclusivityPledge}
                />
              </div>
            </div>

            {/* Do you have images of the gig? */}
            <FileUploadTwo
              label="Do you have images of the gig?"
              onChange={(files) => {
                setGigImages(files);
                handleImageFilesChange(files);
              }}
              accept="image/*"
              multiple={true}
            />
            {imageSizeError && (
              <div style={{ 
                color: '#CB101D', 
                fontSize: '14px', 
                marginTop: '4px',
                fontFamily: 'Rubik'
              }}>
                {imageSizeError}
              </div>
            )}

            {/* Need the operative to have additional qualifications? */}
            <FormSelectTwo
              label="Need the operative to have additional qualifications?"
              value=""
              onChange={(e) => {
                if (e.target.value && !qualifications.includes(e.target.value)) {
                  setQualifications([...qualifications, e.target.value]);
                }
              }}
              options={qualificationOptions}
              placeholder="Choose from our qualifications list"
              caretIcon={caretDown}
              marginBottom="5px"
            />

            {/* Display selected qualifications */}
            {qualifications.length > 0 && (
              <div className="mt-0">
                <div className='flex flex-col gap-1'>
                  <div className='flex flex-wrap gap-2'>
                    {qualifications.map((selectedQualification) => (
                      <Button
                        key={selectedQualification}
                        size='small'
                        rounded={true}
                        elevated={false}
                        onClick={() => handleRemoveQualification(selectedQualification)}
                        className='max-w-xs overflow-hidden truncate border !bg-[#323C58] px-2 py-1 text-xs cursor-pointer'
                      >
                        <Text className='rubik font-normal leading-4 text-[#FFFFFF]'>{selectedQualification}</Text>
                      </Button>
                    ))}
                  </div>
                  {qualifications.length > 0 && (
                    <Button 
                      variant='ghost' 
                      className='rubik py-0 font-medium text-[#3C455D] underline self-start' 
                      onClick={clearAllSelectedQualifications}
                    >
                      Clear all
                    </Button>
                  )}
                </div>
              </div>
            )}

            {/* Any notes or special requirements? */}
            <FormTextAreaTwo
              label="Any notes or special requirements?"
              value={specialRequirements}
              onChange={(e) => setSpecialRequirements(e.target.value)}
              placeholder="Add details like dress code, check-in process, or anything else that’ll help the operative show up prepared."
              rows={5}
            />
          </form>
        </div>
      </div>

      {/* Footer */}
      <footer style={footerStyles}>
        <SecondaryButtonTwo onClick={handleBack}>
          Back
        </SecondaryButtonTwo>
        
        <div style={actionContainerStyles}>
          <PrimaryButtonTwo onClick={handleGetMatches}>
            Get Matches
          </PrimaryButtonTwo>
          <span style={finalStepTextStyles}>
            That's it but you can still edit this later
          </span>
        </div>
      </footer>
    </div>
  );
};

export default JobPostIntroTwo;
