import { Button, Text, View } from 'reshaped';
import { useModalAction } from 'src/context/ModalContext';

const SignUpButton = () => {
  const { openModal } = useModalAction();

  return (
    <Button
      variant='outline'
      type='submit'
      rounded={true}
      className='!bg-[#323c58] border-2 !border-transparent'
      onClick={() => openModal('REGISTER')}
    >
      <View className='flex flex-row justify-center items-center  gap-2 bg-[#323c58]  '>
        <div className='rounded-full w-[30px] h-[30px] bg-[#F4F5F7] flex items-center justify-center'>
          <span className='material-icons-outlined text-[18px]'>
            edit
          </span>
        </div>
        <Text className='rubik font-medium text-[14px] leading-5 text-[#F4F5F7]'>
          Sign up
        </Text>
      </View>
    </Button>
  );
};

export default SignUpButton;
