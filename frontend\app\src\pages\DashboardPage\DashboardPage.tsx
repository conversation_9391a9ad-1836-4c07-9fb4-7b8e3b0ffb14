// @ts-nocheck
import React, { useContext, useState, useEffect, useMemo, useRef } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { Loader, Text, View } from 'reshaped';
import { addDays, addMinutes, differenceInDays, format, parse } from 'date-fns';
import '../../components/Header/HeaderMenu/HeaderMenu.css';
import { getAllJobs } from 'src/services/jobs';
import moment from 'moment';
import { useChatContext } from 'src/context/ChatContext';
import { contractStatuses } from '../chat/components/ChatContracts/contractStatues';
import { getPendingPayments, payEscrow, payOutstanding, getAllInvoices } from 'src/services/contracts';
import { ClientPayNowButton } from '../chat/components/ChatContracts/ChatContractCards/ChatContractButtons/ClientContractButtons';

// Walkthrough integration
import ClientDashboardWalkthrough from 'src/components/Walkthrough/ClientDashboardWalkthrough';
import { useClientDashboardWalkthrough } from 'src/hooks/useClientDashboardWalkthrough';
import { useAuthContext } from 'src/context/AuthContext';

const DashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { contracts, handleGetContracts } = useChatContext();
  const [payments, setPayments] = useState([]);
  const [invoices, setInvoices] = useState([]);
  const [allClientJobs, setAllClientJobs] = useState([]);
  const [isLoadingJobs, setIsLoadingJobs] = useState(false);
  const [isLoadingContracts, setIsLoadingContracts] = useState(false);
  const [isLoadingPayments, setIsLoadingPayments] = useState(false);
  const [isLoadingInvoices, setIsLoadingInvoices] = useState(false);
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastRefreshRef = useRef<number>(Date.now());

  // Walkthrough integration
  const { isClient } = useAuthContext();
  const { shouldShow, markSeen, images } = useClientDashboardWalkthrough();
  const walkthroughVisible = isClient === true && shouldShow === true;

  const fetchAllData = () => {
    console.log('Dashboard: Refreshing data...');
    lastRefreshRef.current = Date.now();
    setIsLoadingJobs(true);
    setIsLoadingContracts(true);
    setIsLoadingPayments(true);
    setIsLoadingInvoices(true);
    getAllJobs().then((data: any) => {
      setAllClientJobs(data.data);
      setIsLoadingJobs(false);
      handleGetContracts().then(() => setIsLoadingContracts(false));
      getPendingPayments().then((res) => {
        setPayments(res?.data || []);
        setIsLoadingPayments(false);
      });
      getAllInvoices().then((res) => {
        setInvoices(res.data);
        setIsLoadingInvoices(false);
      });
    });
  };

  const startPeriodicRefresh = () => {
    // Clear existing interval
    if (refreshIntervalRef.current) {
      clearInterval(refreshIntervalRef.current);
    }
    
    // Set up periodic refresh every 2 minutes
    refreshIntervalRef.current = setInterval(() => {
      console.log('Dashboard: Periodic refresh triggered');
      fetchAllData();
    }, 120000);
  };

  const stopPeriodicRefresh = () => {
    if (refreshIntervalRef.current) {
      clearInterval(refreshIntervalRef.current);
      refreshIntervalRef.current = null;
    }
  };

  // 1. Initial load
  useEffect(() => {
    console.log('Dashboard: Initial load');
    fetchAllData();
    startPeriodicRefresh();
    
    return () => {
      stopPeriodicRefresh();
    };
  }, []);

  // 2. Refresh when navigating back to dashboard (browser navigation)
  useEffect(() => {
    console.log('Dashboard: Location changed, refreshing data');
    fetchAllData();
  }, [location.pathname]);

  // 3. Refresh when page becomes visible again (tab switching)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        console.log('Dashboard: Page became visible, refreshing data');
        fetchAllData();
        startPeriodicRefresh();
      } else {
        console.log('Dashboard: Page hidden, stopping periodic refresh');
        stopPeriodicRefresh();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // 4. Refresh when coming from job posting (check for refresh parameter)
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    if (urlParams.get('refresh') === 'true') {
      console.log('Dashboard: Refresh parameter detected, refreshing data');
      fetchAllData();
      // Clean up the URL parameter
      const newUrl = window.location.pathname;
      window.history.replaceState({}, '', newUrl);
    }
  }, [location.search]);

  // 5. Focus event refresh (when window regains focus)
  useEffect(() => {
    const handleFocus = () => {
      // Only refresh if it's been more than 10 seconds since last refresh
      const timeSinceLastRefresh = Date.now() - lastRefreshRef.current;
      if (timeSinceLastRefresh > 10000) {
        console.log('Dashboard: Window focused, refreshing data');
        fetchAllData();
      }
    };

    window.addEventListener('focus', handleFocus);
    return () => {
      window.removeEventListener('focus', handleFocus);
    };
  }, []);

  const mappedContracts = (contracts || []).map((contract) => ({
    ...contract,
    status: contractStatuses.find((status) => status.value === contract?.status)?.label,
  }));

  const filteredPayments = payments.filter((payment) => payment.pay_amount === '0' )

  return (
    <>
      {/* Client first-visit walkthrough overlay */}
      {walkthroughVisible && (
        <ClientDashboardWalkthrough
          visible={walkthroughVisible}
          images={images}
          onComplete={markSeen}
        />
      )}

      <View className='mx-auto grid w-full grid-cols-1 sm:mx-0 sm:grid-cols-2 sm:gap-[24px] md:px-[10px] lg:grid-cols-4 lg:gap-[24px] '>
      <View className='flex h-[583px] flex-col rounded-lg border border-[#DFE2EA] bg-[#fff]'>
        <Text className='rubik p-[24px] text-left text-[16px] font-medium leading-[20px] text-[#1A1A1A]'>
          Your job postings ({allClientJobs?.length || 0})
        </Text>
        <View className='flex h-full w-full flex-col gap-[16px] overflow-auto border-t border-t-[#DFE2EA] p-[24px]'>
          {isLoadingJobs ? (
            <div className='flex h-full w-full items-center justify-center'>
              <Loader size='medium' className='h-10 w-10' />
            </div>
          ) : (
            (allClientJobs || []).map((job: any, index) => {
              let formattedDate = moment(job.updated_at).format('ddd D MMM YYYY');

              return (
                <div
                  key={job.id + '_' + index}
                  className='flex flex-col items-start justify-center gap-[20px] self-stretch rounded-[8px] border border-[#DFE2EA] !bg-[#F4F5F7] p-[16px] '
                >
                  <View className='flex flex-col items-start gap-[8px] '>
                    <Text className='rubik text-[13px] font-normal leading-[16px] text-[#444B5F] '>{formattedDate}</Text>
                    <Text className='rubik max-w-[180px] overflow-hidden truncate whitespace-normal text-left text-[16px] font-medium leading-[20px] text-[#000] '>
                      {job.post_name}
                    </Text>
                  </View>
                  <View className='flex w-full flex-row items-center justify-between'>
                    <button
                      className='rubik btn-no-hover !py-0 text-[13px] font-normal leading-[16px] !text-[#0B80E7] '
                      onClick={() => navigate(`/manage-jobs/${job?.id}`, { state: { job } })}
                    >
                      Review
                    </button>
                    <Text className='rubik text-[13px] font-medium leading-[16px] text-[#323C58] '>{job.applicants?.length} proposals</Text>
                  </View>
                </div>
              );
            })
          )}
        </View>
      </View>
      <View className='mt-[20px] flex h-[583px] flex-col rounded-lg border border-[#DFE2EA] bg-[#fff] sm:mt-0'>
        <Text className='rubik p-[24px] text-left text-[16px] font-medium leading-[20px] text-[#1A1A1A]'>
          Contracts started ({mappedContracts?.length || 0})
        </Text>
        <View className='flex h-full w-full flex-col gap-[16px] overflow-auto border-t border-t-[#DFE2EA] p-[24px]'>
          {isLoadingContracts ? (
            <div className='flex h-full w-full items-center justify-center'>
              <Loader size='medium' className='h-10 w-10' />
            </div>
          ) : (
            (mappedContracts || []).map((contract: any) => {
              const statusColor =
                contract.status === 'Invited'
                  ? '#3B3B3D'
                  : contract.status === 'Pending'
                    ? '#323C58'
                    : contract.status === 'In progress'
                      ? '#0B80E7'
                      : contract.status === 'Completed'
                        ? '#05751F'
                        : contract.status === 'Cancelled'
                          ? '#CB101D'
                          : '#323C58';
              return (
                <div
                  key={contract.id + '_' + contract.job?.id}
                  onClick={() => navigate(`/chat/${contract?.chat_id}`)}
                  className='border-primary flex cursor-pointer flex-col items-start justify-center gap-[12px] self-stretch rounded rounded-[8px] border !bg-[#F4F5F7] p-[16px]'
                >
                  <Text className=' rubik text-left  text-[14px] font-medium leading-[16px] text-[#1A1A1A]'>{contract.job?.post_name}</Text>
                  <View className='border-primary flex h-[40px] w-full items-center justify-center self-stretch rounded-[4px] border !bg-[#DBDFEA] px-[8px] py-[4px]'>
                    <Text className='rubik text-[14px] font-medium leading-[20px]'>
                      <span className='!font-normal'>Status:</span>
                      &nbsp;
                      <span className={`text-[${statusColor}]`}>{contract.status}</span>
                    </Text>
                  </View>
                </div>
              );
            })
          )}
        </View>
      </View>
      <View className='mt-[20px] flex h-[583px] flex-col rounded-lg border border-[#DFE2EA] bg-[#fff] sm:mt-0'>
        <Text className='rubik w-full p-[24px] text-left text-[16px] font-medium leading-[20px] text-[#1A1A1A]'>Payments ({filteredPayments?.length || 0})</Text>
        <View className='flex h-full w-full flex-col gap-[16px] overflow-auto border-t border-t-[#DFE2EA] p-[24px]'>
          {isLoadingPayments ? (
            <div className='flex h-full w-full items-center justify-center'>
              <Loader size='medium' className='h-10 w-10' />
            </div>
          ) : (
            (filteredPayments || []).map((payment) => {
              const contractOrJobName = payment?.job?.id ? payment?.job?.post_name : '#' + payment?.id;
              const [datePart, timePart] = payment?.ends?.split(' - ');
              const parsedDate = parse(datePart, 'yyyy-MM-dd', new Date());
              const [hours, minutes] = timePart.split(':');
              const resultDate = addMinutes(parsedDate, parseInt(hours) * 60 + parseInt(minutes));
              const formattedDate = format(resultDate, 'iii d MMMM Y');
              const paymentTerms = payment?.payment_terms || 10;

              const currentDate = new Date();
              const targetDate = addDays(parsedDate, paymentTerms);
              const daysDifference = differenceInDays(targetDate, currentDate);

              const handlePayEscrow = async (cardId: number, paymentMethodId: string) => {
                const payment = await payEscrow(cardId, paymentMethodId);
                return payment;
              };

              const handlePayOutstanding = async (cardId: number) => {
                const outstandingPayment = await payOutstanding(cardId, payments?.id);
                return outstandingPayment;
              };

              if (payment.pay_amount == 0) return

              return (
                <View
                  key={payment.id}
                  className='border-primary flex flex-col items-start justify-center gap-[12px] self-stretch rounded rounded-[8px] border !bg-[#F4F5F7] p-[16px] '
                >
                  <View className='text-left'>
                    <View className='flex flex-row items-center justify-between gap-[8px]'>
                      <Text className=' rubik  text-left text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>
                        {payment?.operative?.name} {' - '} {contractOrJobName}
                      </Text>
                      <span className='material-icons-outlined text-[18px] !text-[#0B80E7]'>check</span>
                    </View>
                    <Text className=' rubik text-[13px] font-normal leading-[20px] text-[#444B5F] '>{formattedDate}</Text>
                  </View>
                  <View className='border-primary flex h-[40px] w-full items-center justify-center self-stretch rounded-[4px] border bg-[#DBDFEA] px-[8px] py-[4px]'>
                    <Text className='rubik text-[13px] font-medium leading-[20px] !text-[#323C58] '>
                      <span className='!font-normal '>{payment.escrow_status === 'paid' ? 'Outstanding' : 'Escrow'}:</span>
                      &nbsp; £{payment.escrow_status === 'paid' ? payment.pay_amount : payment.escrow_amount}
                    </Text>
                  </View>
                  {daysDifference !== '' && (
                    <Text className='  rubik flex w-full items-center justify-center text-[14px] font-normal leading-[20px] text-[#444B5F]  '>
                      {daysDifference} days left
                    </Text>
                  )}
                  <div className='h-[1px] w-full !bg-[#DFE2EA]' />
                  <ClientPayNowButton
                    payEscrow={handlePayEscrow}
                    payOutstanding={handlePayOutstanding}
                    contract={payment}
                    fromDashboard={payment.escrow_status === 'paid' ? 'Pay now' : 'Pay Escrow'}
                  />
                </View>
              );
            })
          )}
        </View>
      </View>
      <View className='mt-[20px] flex h-[583px] flex-col rounded-lg border border-[#DFE2EA] bg-[#fff] sm:mt-0'>
        <Text className='rubik w-full p-[24px] text-left text-[16px] font-medium leading-[20px] text-[#1A1A1A]'>Invoices ({invoices?.length || 0})</Text>
        <View className='flex h-full w-full flex-col gap-[16px] overflow-auto border-t border-t-[#DFE2EA] p-[24px]'>
          {isLoadingInvoices ? (
            <div className='flex h-full w-full items-center justify-center'>
              <Loader size='medium' className='h-10 w-10' />
            </div>
          ) : (
            (invoices || []).map((invoice) => {
              const contract = invoice?.contract;
              const contractId = contract?.id;
              const contractOrJobName = contract?.job?.id ? contract?.job?.post_name : 'Individual contract #' + contract?.id;
              const [datePart, timePart] = contract?.updated_at?.split('T');
              const [hours, minutes] = timePart.split(':');
              const parsedDate = parse(datePart, 'yyyy-MM-dd', new Date());
              const resultDate = addMinutes(parsedDate, parseInt(hours) * 60 + parseInt(minutes));
              const formattedDate = format(resultDate, 'iii d MMMM Y');

              return (
                <Link key={invoice.client.id + '-' + invoice.contract.id} target='_blank' className='w-full' to={`/invoice/${contractId}`}>
                  <View
                    key={contractId}
                    className='border-primary flex flex-col items-start justify-center gap-[12px] self-stretch rounded-[8px] border !bg-[#F4F5F7] p-[16px] '
                  >
                    <View className=''>
                      <Text className='rubik text-left text-[14px] font-medium leading-[20px] text-[#1A1A1A]'>{contractOrJobName}</Text>
                      <Text className='rubik text-left text-[13px] font-normal leading-[20px] text-[#444B5F] '>{formattedDate}</Text>
                    </View>
                    <View className='border-primary flex h-[40px] w-full items-center justify-center self-stretch rounded-[4px] border bg-[#CDEDD5] px-[8px] py-[4px]'>
                      <Text className='rubik text-[13px] font-medium leading-[20px] !text-[#323C58]'>
                        <span className='!font-normal '>Total:</span>
                        &nbsp; £{contract?.total_amount}
                      </Text>
                    </View>
                  </View>
                </Link>
              );
            })
          )}
        </View>
      </View>
    </View>
    </>
  );
};

export default DashboardPage;
