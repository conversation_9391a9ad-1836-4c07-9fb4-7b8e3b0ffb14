import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Text, Image, Card, View, Icon, Divider, Button, useToast } from 'reshaped';
import { IdVerified } from 'src/assets/icons';
import surleyicon from '../../assets/icons/surleyicon/surleyicon.png';

const getValidImageUrl = (image: string | undefined) => {
  if (!image?.includes('https://app.surelysecurity.com/storage/')) return 'https://app.surelysecurity.com/storage/' + image;
  return image;
};

const SearchOperatorCard = ({
  operator,
  addFavorite,
  removeFavorite,
  fetchAllOperative,
}: {
  operator: any;
  addFavorite: any;
  removeFavorite: any;
  fetchAllOperative: any;
}) => {
  const toast = useToast();
  const navigate = useNavigate();

  const [isFavourite, setIsFavourite] = useState(operator.is_favorite || false);

  const lastname = operator.name?.split(' ').pop()?.charAt(0).toUpperCase() + operator.name?.split(' ').pop()?.slice(1);
  const firstname = operator.name?.split(' ').shift()?.charAt(0).toUpperCase() + operator.name?.split(' ').shift()?.slice(1);
  const initialName = `${firstname || ''} ${lastname || ''}`;
  const onlycity = operator.address_2?.split(', ', 1).pop();

  return (
    <Card key={operator.name} className='h-[100%] cursor-pointer rounded-[8px] p-6 shadow-md xl:w-[312px]'>
      <View className='flex h-[100%]  flex-col gap-5'>
        <View className='flex flex-col gap-5'>
          <View className='flex items-center justify-between '>
            <div className='flex items-center gap-[8px]'>
              {operator.sia_certificates?.[0]?.sia_licence === 1 ? (
                <span className='rubik  h-[auto] w-[auto] rounded-md !bg-[#E6FEF3] p-1 text-xs font-normal leading-4 text-[#05751F]'>
                  SIA CERTIFIED
                </span>
              ) : (
                <span className='rubik  h-[auto] w-[auto] rounded-md !bg-[#EDEDED] p-1 text-xs font-normal leading-4 text-[#6A6A6A]'>
                  SIA PENDING
                </span>
              )}
              {operator.instant_book ? (
                <span className='rubik h-[auto] w-[auto] rounded-md !bg-[#D1E1FF] px-[8px] py-1 text-xs font-normal leading-4 text-[#0D2F87]'>
                  INSTANT BOOK
                </span>
              ) : null}
            </div>
            <span
              onClick={() => {
                setIsFavourite((prevFavourite: any) => !prevFavourite);
                if (isFavourite) {
                  removeFavorite(operator.id);
                  toast.show({
                    title: 'Done!',
                    text: 'Removed from favorites',
                    startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
                  });
                  fetchAllOperative();
                } else {
                  addFavorite(operator.id);
                  toast.show({
                    title: 'Done!',
                    text: 'Added to favorites',
                    startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
                  });
                  fetchAllOperative();
                }
              }}
              className={`material-icons-outlined  -mt-1  !text-[20px] text-base text-[#BBC1D3] ${isFavourite ? 'text-red-500' : 'text-[#BBC1D3]'}`}
            >
              favorite
            </span>
          </View>

          <a
            href={`/operator-profile/${operator.id}`}
            onClick={(e) => {
              e.preventDefault();
              navigate(`/operator-profile/${operator.id}`);
            }}
            className=' flex flex-col gap-5'
          >
            <View className=' flex flex-row items-center gap-4'>
              <View>
                {operator.profile_photo ? (
                  <div
                    className='rounded-full p-0.5'
                    style={{
                      background: 'linear-gradient(180deg, rgba(50, 60, 88, 0.855), rgba(113, 225, 248, 0.9025))',
                    }}
                  >
                    <Image
                      className='flex h-[80px] w-[80px] flex-col items-start rounded-full bg-white'
                      alt='Profile'
                      src={getValidImageUrl(operator.profile_photo)}
                    />
                  </div>
                ) : (
                  <View className='flex h-[80px]  w-[80px] items-center justify-center  rounded-full bg-[#0B80E7]'>
                    <Text className='rubik text-[30px] uppercase text-white'>{operator.name?.charAt(0)}</Text>
                  </View>
                )}

                {operator.sia_certificates?.[0]?.id_check === 1 && <Icon className='absolute bottom-0 right-0 h-[31px] w-[30px]' svg={IdVerified} />}
              </View>
              <View className='flex flex-col justify-start gap-1'>
                {operator.overall_ratings?.count > 0 && (
                  <div className='flex flex-row gap-2'>
                    <span className='material-icons text-[20px] text-[#F4BF00]'>star</span>
                    <Text className='rubik text-[15px] font-medium leading-[20px] text-[#323c58]'>
                      {operator.overall_ratings.value} ({operator.overall_ratings.count})
                    </Text>
                  </div>
                )}
                <View>
                  <Text className='rubik text-[16px] font-medium leading-[20px] text-[#1A1A1A]'>{initialName}</Text>
                </View>
                <View className='flex items-center'>
                  {operator.address_2 ? (
                    <>
                      <Text className='rubik text-[15px] font-normal leading-[20px] text-[#0B80E7]'>{onlycity}</Text>
                      <span className='material-icons-outlined icon-line-height mt-[5px] p-0 pb-1 text-[20px] text-[#0B80E7]'>place</span>
                    </>
                  ) : (
                    <></>
                  )}
                </View>
              </View>
            </View>
            {/* <View className='flex flex-col items-start gap-5'>
              <Text className=' font-medium rubik text-[#lalala]'>Jobs completed: {operator?.jobs?.completed_jobs}</Text>
              
            </View> */}
            <Divider className=' h-[1px] w-full' />
            <View className=' flex  flex-wrap gap-2'>
              {operator.sia_licence_types?.map((licenceType: string, licenceIndex: number) => (
                <Button
                  size='small'
                  key={licenceIndex}
                  rounded={true}
                  elevated={false}
                  className='max-w-xs overflow-hidden truncate border border-[#05751F] !bg-[#E6FEF3] px-2 py-1 text-xs text-[#323c58] '
                >
                  <Text color='positive' className='rubik flex items-center gap-1'>
                    <span className='material-icons text-[16px]'>star</span>
                    {licenceType}
                  </Text>
                </Button>
              ))}
              {operator.sia_certificates?.[0]?.sia_licence === 1 &&
                operator.sia_certificates?.[0]?.id_check === 1 &&
                operator.sia_certificates?.[0]?.proof_of_address === 1 &&
                operator.sia_certificates?.[0]?.employment_history === 1 &&
                operator.sia_certificates?.[0]?.credit_check === 1 &&
                operator.sia_certificates?.[0]?.no_criminal_record === 1 && (
                  <View className='gap-2'>
                    <Button
                      size='small'
                      rounded={true}
                      elevated={false}
                      className='mr-[5px] max-w-xs overflow-hidden  truncate border !bg-[#7CDBEF] px-2 py-1 text-xs text-[#323c58] '
                    >
                      <Text className='rubik flex items-center gap-1 text-[12px] font-medium text-[#323c58]'>
                        <span className='material-icons text-[16px] text-[#323c58]'>security</span>
                        BS7858
                      </Text>
                    </Button>
                  </View>
                )}
            </View>
          </a>
        </View>
        <a
          href={`/operator-profile/${operator.id}`}
          onClick={(e) => {
            e.preventDefault();
            navigate(`/operator-profile/${operator.id}`);
          }}
          className='mt-auto flex  w-full justify-end'
        >
          <Button
            endIcon={() => <span className='material-icons p-0 pb-1 text-[15px] text-[#0B80E7]'>arrow_forward_ios</span>}
            className='btn-no-hover mb-[0px] flex h-[32px] w-[87px] items-center justify-center  gap-2  !bg-transparent p-1'
          >
            <span className='rubik text-[16px] font-medium text-[#0B80E7]'>More</span>
          </Button>
        </a>
      </View>
    </Card>
  );
};

export default SearchOperatorCard;
