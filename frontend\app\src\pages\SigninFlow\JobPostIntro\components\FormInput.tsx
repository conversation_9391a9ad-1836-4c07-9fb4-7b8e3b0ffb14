import React, { CSSProperties } from 'react';
import { labelStyles, inputStyles, iconInputStyles, selectContainerStyles, iconStyles } from '../styles';

// Unique class name for our input to scope the styles
const inputClassName = 'job-post-input';

interface FormInputProps {
  label: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
  icon?: string;
  type?: string;
  onClick?: () => void;
  readOnly?: boolean;
  style?: CSSProperties;
}

const FormInput: React.FC<FormInputProps> = ({ 
  label, 
  value, 
  onChange, 
  placeholder = '', 
  icon, 
  type = 'text',
  onClick,
  readOnly = false,
  style
}) => {
  return (
    <div style={{ marginBottom: '24px' } as CSSProperties}>
      <style>{`
        .${inputClassName}::placeholder {
          font-family: 'Rubik';
          font-weight: 300;
          font-style: normal;
          font-size: 16px;
          line-height: 100%;
          letter-spacing: 0%;
          opacity: 1;
        }
      `}</style>
      <label style={labelStyles as CSSProperties}>
        {label}
      </label>
      <div style={icon ? (selectContainerStyles as CSSProperties) : undefined}>
        {icon && (
          <img
            src={icon}
            alt=""
            style={{
              ...iconStyles,
              cursor: onClick ? 'pointer' : 'default'
            } as CSSProperties}
            onClick={onClick}
          />
        )}
        <input
          type={type}
          value={value}
          onChange={onChange}
          onClick={onClick}
          readOnly={readOnly}
          style={{
            ...(icon ? iconInputStyles : inputStyles),
            ...style
          } as CSSProperties}
          placeholder={placeholder}
          className={inputClassName}
        />
      </div>
    </div>
  );
};

export default FormInput;
