// @ts-nocheck
import { createContext, useContext, FunctionComponent, ReactNode, useState, useEffect } from 'react';
// import Cookies from 'js-cookie';

import { User } from '../types/user';
import { storeUserData } from '../utils/userStorage';
import sessionManager from '../services/sessionManager';

interface AuthProviderProps {
  children: ReactNode;
}

interface AuthContextType {
  isAuthenticated: boolean;
  isClient: boolean | null;
  user: {
      name: any;
      account_type: any;
      email_verified: any;
      created_at(created_at: any): unknown; profile: any 
};
  authenticateUser: (data: any) => void;
  unAuthenticateUser: () => void;
  updateUser: () => void;
  updateUserEmailVerification: () => void;
}

const initialState: AuthContextType = {
  isAuthenticated: false,
  isClient: null,
  user: { profile: {} },
  authenticateUser: () => {},
  unAuthenticateUser: () => {},
  updateUser: () => {},
  updateUserEmailVerification: () => {},
};

const AuthContext = createContext(initialState);

const AuthProvider: FunctionComponent<AuthProviderProps> = ({ children }) => {
  const storedUserData = localStorage.getItem('userData') && JSON.parse(localStorage.getItem('userData') || '');

  const [user, setUser] = useState<User | null>({ profile: storedUserData });
  const [isAuthenticated, setIsAuthenticated] = useState(!!localStorage.getItem('authToken'));

  useEffect(() => {
    setIsAuthenticated(!!localStorage.getItem('authToken'));
    localStorage.setItem('wpRedirect', 'false')
  }, [localStorage.getItem('authToken')]);

  // const setAccessTokenCookie = (token) => {
  //   Cookies.set(TOKEN_COOKIE_NAME, token, { sameSite: 'strict' });
  // };

  // const getAccessTokenFromCookie = () => {
  //   return Cookies.get(TOKEN_COOKIE_NAME);
  // };

  // const removeAccessTokenCookie = () => {
  //   Cookies.remove(TOKEN_COOKIE_NAME);
  // };

  const storeTokenInLocalStorage = (token: string) => {
    localStorage.setItem('authToken', token);
  };

  const removeTokenFromLocalStorage = () => {
    localStorage.removeItem('authToken');
    localStorage.removeItem('linkedIn_code');
    localStorage.removeItem('google_token');
  };

  // const isClient = user?.account_type === 2 ? true : user?.account_type === 1 ? false : null;

  const isClient = user?.profile?.account_type == 2 ? true : user?.profile?.account_type == 1 ? false : null;

  const authenticateUser = (userData: any) => {
    if (userData.token) {
      let parsedUserData = {};
      if (userData.data?.user) {
        parsedUserData = {
          applications: userData?.data?.applications,
          employments: userData?.data?.employments,
          languages: userData?.data?.languages,
          qualifications: userData?.data?.qualifications,
          sia_certificates: userData?.data?.sia_certificates,
          profile: { ...userData?.data?.user, account_type: +userData?.data?.user?.account_type },
        };
      } else {
        parsedUserData = {
          applications: userData?.data?.applications,
          employments: userData?.data?.employments,
          languages: userData?.data?.languages,
          qualifications: userData?.data?.qualifications,
          sia_certificates: userData?.data?.sia_certificates,
          profile: { ...userData?.data, account_type: +userData?.data?.account_type },
        };
      }
      storeTokenInLocalStorage(userData.token);
      setIsAuthenticated(true);
      setUser(parsedUserData);
      localStorage.setItem('wpRedirect', 'false')
      localStorage.setItem('userData', JSON.stringify(parsedUserData?.profile));
      
      sessionManager.startSessionTimer();
      
      window.addEventListener('sessionExpired', () => {
        unAuthenticateUser();
        navigate('/login?session=expired');
      });

      window.addEventListener('sessionWarning', (e: CustomEvent) => {
        // Note: Toast functionality removed due to component hierarchy
        // This would need to be handled at a higher level where ToastProvider is available
        console.warn('Session Expiring', `Your session will expire in ${e.detail.timeLeft} seconds. Please save your work.`);
      });
    }
  };

  const unAuthenticateUser = () => {
    removeTokenFromLocalStorage();
    setIsAuthenticated(false);
    setUser({});
    localStorage.removeItem('userData');
  };

  const handleLogin = async (credentials) => {
    try {
      const response = await AuthService.login(credentials);
      if (response?.data?.user) {
        const userData = {
          id: response.data.user.profile.id,
          name: response.data.user.profile.name,
          profile_photo: response.data.user.profile.profile_photo,
        };
        localStorage.setItem('userData', JSON.stringify(userData));
        // Rest of login logic
      }
    } catch (error) {
      console.error('Login failed:', error);
    }
  };

  const updateUserEmailVerification = () => {
    if (user?.profile) {
      setUser({
        ...user,
        profile: {
          ...user.profile,
          email_verified: true
        }
      });
      // Also update localStorage
      const storedUserData = JSON.parse(localStorage.getItem('userData') || '{}');
      localStorage.setItem('userData', JSON.stringify({
        ...storedUserData,
        email_verified: true
      }));
    }
  };

  useEffect(() => {
    // Listen for deployment-forced session expiry
    window.addEventListener('sessionExpired', () => {
      unAuthenticateUser();
      navigate('/login?session=deployment');
    });

    return () => {
      window.removeEventListener('sessionExpired', () => {});
    };
  }, []);

  return (
    <AuthContext.Provider
      value={{
        ...initialState,
        isAuthenticated,
        isClient,
        authenticateUser,
        unAuthenticateUser,
        updateUserEmailVerification,
        user,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

const useAuthContext = () => {
  const context = useContext(AuthContext);
  return context;
};

export { AuthContext, AuthProvider, useAuthContext };
