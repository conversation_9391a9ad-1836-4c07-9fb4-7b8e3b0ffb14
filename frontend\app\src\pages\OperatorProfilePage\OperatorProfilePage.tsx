// @ts-nocheck
import React, { useState, useEffect, useContext } from 'react';
import CompleteProfileToast from 'src/components/Profile/OperatorProfileComponents/CompleteProfileToast/CompleteProfileToast';
import OperatorBio from 'src/components/Profile/OperatorProfileComponents/OperatorBio/OperatorBio';
import OperatorEmploymentHistory from 'src/components/Profile/OperatorProfileComponents/OperatorEmploymentHistory/OperatorEmploymentHistory';
import OperatorOverallRating from 'src/components/Profile/OperatorProfileComponents/OperatorOverallRating/OperatorOverallRating';
import OperatorRelevantQualification from 'src/components/Profile/OperatorProfileComponents/OperatorRelevantQualifications/OperatorRelevantQualifications';
import OperatorSecurityAchievement from 'src/components/Profile/OperatorProfileComponents/OperatorSecurityAchievement/OperatorSecurityAchievement';

import { AppContext } from 'src/context/AppContext';

import { Button, Text, View, TextField, Image, useToggle } from 'reshaped';
import surleyicon from '../../assets/icons/surleyicon/surleyicon.png';
import BankAccountToast from 'src/components/Profile/OperatorProfileComponents/CompleteProfileToast/BankAccountToast';
import { useNavigate } from 'react-router-dom';
import { useToastSystem } from 'src/context/ToastSystemContext';

export const OperatorProfilePage = () => {
  const {
    profileTitle,
    name,
    cityInfo,
    profilePhoto,
    profileVideo,
    siaLicense,
    additionalPictures,
    instantBook,
    siaLicence,
    idCheck,
    proofOfAddress,
    employmentHistory,
    creditCheck,
    noCriminalRecord,
    profileDescription,
    positions,
    qualifications,
    industrySectors,
    testlang,
    languages,
    addressVerificationDocument,
    documentType,
    idBackDocument,
    idFrontDocument,
    selfieVerificationDocument,
    siaLicenceCardPhoto,
    siaLicenceExpiryDate,
    postalCity,
    siaLicenceNumber,
    hasBankAccount,
  } = useContext(AppContext);
  const navigate = useNavigate();
  const toastSystem = useToastSystem();

  const [showBankToast, setShowBankToast] = useState(!hasBankAccount);

  const isDataUnavailable =
    profileTitle === null ||
    profileDescription === null ||
    profileDescription === undefined ||
    industrySectors === null ||
    profilePhoto === null ||
    profileVideo === null ||
    siaLicense === null ||
    additionalPictures === null ||
    testlang === null ||
    profileTitle === undefined ||
    industrySectors === undefined ||
    profilePhoto === undefined ||
    profileVideo === undefined ||
    siaLicense === undefined ||
    additionalPictures === undefined ||
    testlang === undefined ||
    positions.length === 0 ||
    industrySectors.length === 0 ||
    qualifications.length === 0;

  const isBankAccountUnavailable = hasBankAccount === null || undefined;

  useEffect(() => {
    if (isBankAccountUnavailable) {
      toastSystem.showWelcome(
        'In order to start working, you will need to fill in your bank account details in the payment settings section.',
        {
          text: 'Proceed to settings',
          onClick: () => navigate('/operator-settings-payment', { state: { activeTab: '0' } })
        }
      );
    }
  }, [isBankAccountUnavailable, navigate]);

  useEffect(() => {
    if (isDataUnavailable) {
      toastSystem.showWelcome(
        'Enhance your chances of being selected by companies looking for security operatives like yourself by completing your profile.',
        {
          text: 'Complete your profile',
          onClick: () => navigate('/operator-settings')
        }
      );
    }
  }, [isDataUnavailable, navigate]);

  return (
    <div className='flex flex-col gap-6'>
      <div className='flex flex-col gap-6 lg:flex-row'>
        <OperatorBio />
        <OperatorSecurityAchievement />
      </div>
      {/* <OperatorOverallRating /> */}
      <div className='flex flex-col gap-6 lg:flex-row'>
        <OperatorRelevantQualification />
        <OperatorEmploymentHistory />
      </div>
      {/* {isDataUnavailable && <CompleteProfileToast showBankToast={showBankToast} />}
      {showBankToast && <BankAccountToast onClose={() => setShowBankToast((prev) => !prev)} />} */}


    </div>
  );
};

export default OperatorProfilePage;
