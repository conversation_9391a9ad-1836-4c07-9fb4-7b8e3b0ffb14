import React from 'react';
import { labelStyles } from '../../JobPostIntro/styles';

interface FormInputTwoProps {
  label: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
  icon?: string;
  onClick?: () => void;
  readOnly?: boolean;
  style?: React.CSSProperties;
}

const FormInputTwo: React.FC<FormInputTwoProps> = ({
  label,
  value,
  onChange,
  placeholder = '',
  icon,
  onClick,
  readOnly = false,
  style = {}
}) => {
  const inputContainerStyles: React.CSSProperties = {
    marginBottom: '24px',
  };

  const inputStyles: React.CSSProperties = {
    width: '100%',
    padding: '16px',
    border: '1px solid #ddd',
    borderRadius: '8px',
    fontSize: '16px',
    fontFamily: 'Rubik, sans-serif',
    ...style
  };

  const iconContainerStyles: React.CSSProperties = {
    position: 'absolute',
    right: '16px',
    top: '50%',
    transform: 'translateY(-50%)',
    cursor: 'pointer',
  };

  return (
    <div style={inputContainerStyles}>
      <label style={labelStyles}>
        {label}
      </label>
      <div style={{ position: 'relative', marginTop: '8px' }}>
        <input
          type="text"
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          style={inputStyles}
          onClick={onClick}
          readOnly={readOnly}
        />
        {icon && (
          <div style={iconContainerStyles}>
            <img src={icon} alt="Input Icon" style={{ width: '24px', height: '24px' }} />
          </div>
        )}
      </div>
    </div>
  );
};

export default FormInputTwo;
