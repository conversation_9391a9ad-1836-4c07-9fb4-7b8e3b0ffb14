<?php

namespace App\Http\Controllers\Api;

use App\DataTables\SupportDataTable;
use App\Http\Controllers\Controller;
use App\Models\Contract;
use App\Models\Support;
use App\Models\User;
use App\Notifications\AskForSupportNotification;
use Exception;
use Illuminate\Http\Request;
use SendGrid\Mail\Mail;

class SupportController extends Controller
{
    use \App\Traits\Helpers;

    public function store(Request $request, $contractId)
    {
        $contract = Contract::find($contractId);

        if (!$contract) {
            return response()->json([
                'error' => true,
                'message' => 'No contract found with this id!'
            ]);
        }

        // Allow support requests for pending, in progress, completed, or canceled contracts
        // Check both numeric constants and string representations
        $allowedStatuses = [Contract::pending, Contract::in_progress, Contract::complete, Contract::canceled, 'Pending', 'In progress', 'Completed', 'Canceled'];
        
        if (!in_array($contract->status, $allowedStatuses)) {
            return response()->json([
                'error' => true,
                'message' => 'Contract must be pending, in progress, completed, or canceled to ask for support!'
            ]);
        }

        if (auth()->id() != $contract->operative_id && auth()->id() != $contract->client_id) {
            return response()->json([
                'error' => true,
                'message' => 'Cannot ask for support! You are not part of this contract!'
            ]);
        }

        if ($request->get('comment') == '') {
            return response()->json([
                'error' => true,
                'message' => "Comment shouldn't be an empty string",
            ]);
        }

        if ($request->has('files')) {
            $documents = [];
            if (count($request->get('files')) > 3) {
                return response()->json([
                    'error' => true,
                    'message' => "Cannot upload more than three files!",
                ]);
            }

            foreach ($request->get('files') as $file) {
                $fileSizeInBytes = mb_strlen($file, '8bit');
                $fileSizeInMB = $fileSizeInBytes / (1024 * 1024);
                if ($fileSizeInMB > 3) {
                    return response()->json([
                        'error' => true,
                        'message' => "Size of the files shouldn't be more than 3mb",
                    ]);
                }
                $documents[] = $this->base64Upload('support_files', $file);
            }

            $data['documents'] = json_encode($documents);
        }

        $data['author_id'] = auth()->id();
        $data['contract_id'] = $contract->id;
        $data['comment'] = $request->get('comment');

        if (!Support::create($data)) {
            return response()->json([
                'error' => true,
                'message' => 'Ask for support cannot be submitted!'
            ]);
        }
        
        $infoUser = new User(['email' => '<EMAIL>']);
        $infoUser->notify(new AskForSupportNotification(auth()->user()->name, auth()->user()->email));

        return response()->json([
            'error' => false,
            'message' => 'Ask for support submitted successfully!'
        ]);
    }

    public function table(SupportDataTable $dataTable) {
        return $dataTable->render('support.index');
    }

    public function getDetails($id) {
        $support = Support::query()
            ->leftJoin('mobile_users as author', 'author.id', 'supports.author_id')
            ->leftJoin('contracts as contract', 'contract.id', 'supports.contract_id')
            ->leftJoin('mobile_users as operative', 'operative.id', 'contract.operative_id')
            ->leftJoin('mobile_users as client', 'client.id', 'contract.client_id')
            ->leftJoin('jobs as job', 'job.id', 'contract.job_id')
            ->select([
                'supports.id as id',
                'author.name as name',
                'author.email as email',
                'author.phone as phone',
                'job.post_name as job_post',
                'contract.id as contract_id',
                'contract.hourly_rate as hourly_rate',
                'contract.location as contract_location',
                'contract.start_date as contract_start_date',
                'contract.end_date as contract_end_date',
                'contract.status as contract_status',
                'operative.name as operative_name',
                'operative.email as operative_email',
                'operative.phone as operative_phone',
                'client.name as client_name',
                'client.email as client_email',
                'client.phone as client_phone',
                'supports.created_at as date',
                'supports.comment as comment',
            ])
            ->where('supports.id', $id)
            ->first();

        if (!$support) {
            return response()->json([
                'error' => true,
                'message' => 'Support request not found'
            ], 404);
        }

        return response()->json([
            'error' => false,
            'data' => $support
        ]);
    }
}
