import React from 'react';
import { secondaryButtonStyles } from '../../JobPostIntro/styles';

interface SecondaryButtonTwoProps {
  children: React.ReactNode;
  onClick: () => void;
}

const SecondaryButtonTwo: React.FC<SecondaryButtonTwoProps> = ({ children, onClick }) => {
  return (
    <button
      type="button"
      onClick={onClick}
      style={{
        ...secondaryButtonStyles,
        width: 126,
        height: 65,
        borderRadius: 20
      }}
    >
      {children}
    </button>
  );
};

export default SecondaryButtonTwo;