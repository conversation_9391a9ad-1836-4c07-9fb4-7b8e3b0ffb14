// @ts-nocheck
import { HttpClient } from 'src/client/http-client';
import { AUTH_TOKEN_KEY } from 'src/utils/constants';

export default class Contract {
  private static cache = new Map();
  private static cacheTimeout = 2000; // 2 seconds

  static async list() {
    const url = '/contracts?';
    try {
      const response = await HttpClient.get<any>(url);
      if (!response.error) {
        return response.data;
      }
    } catch (error) {
      console.error(error);
    }
  }

  static async get(id: string | number) {
    const cacheKey = `contract_${id}`;
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }

    const url = '/contracts/' + id;
    try {
      const response = await HttpClient.get<any>(url);
      if (!response.error) {
        this.cache.set(cacheKey, {
          data: response.data,
          timestamp: Date.now()
        });
        return response.data;
      }
    } catch (error) {
      if (error?.response?.status === 429) {
        // Return cached data if available when rate limited
        if (cached) {
          return cached.data;
        }
      }
      console.error(error);
    }
  }

  static async create(payload: any) {
    const url = `/chats/${payload.chat_id}/contracts`;
    try {
      const response = await HttpClient.post<any>(url, payload);
      if (!response.error) return response;
    } catch (error) {
      console.error(error);
    }
  }

  static async update(contractId: string | number, payload: any) {
    const url = `/contracts/${contractId}`;
    try {
      const response = await HttpClient.put<any>(url, payload);
      if (!response.error) return response;
    } catch (error) {
      console.error(error);
    }
  }

  //client cancels contract
  static async cancel(chatId: string | number, contractId: string | number) {
    const url = `/chats/${chatId}/contract/${contractId}/status`;

    try {
      const response = await HttpClient.post<any>(url, {
        status: 'cancelled',
        type: 'client_cancelled_contract',
      });
      if (!response.error) return response;
    } catch (error) {
      console.error(error);
    }
  }

  //operative refuses contract
  static async refuse(chatId: string | number, contractId: string | number) {
    const url = `/chats/${chatId}/contract/${contractId}/status`;

    try {
      const response = await HttpClient.post<any>(url, {
        status: 'cancelled',
        type: 'operator_refused_contract',
      });
      if (!response.error) return response;
    } catch (error) {
      console.error(error);
    }
  }

  //operative refuses contract
  static async accept(chatId: string | number, contractId: string | number) {
    const url = `/chats/${chatId}/contract/${contractId}/status`;

    try {
      const response = await HttpClient.post<any>(url, {
        status: 'in_progress',
        type: 'operator_accepted_contract',
      });
      if (!response.error) return response;
    } catch (error) {
      console.error(error);
    }
  }
}

export const completeContract = async (contractId: string) => {
  const url = `/contracts/${contractId}/complete`;

  try {
    const response = await HttpClient.post<any>(url, {});
    if (!response.error) return response;
  } catch (error) {
    console.error(error);
  }
};

export const operatorReview = async ({ id, input }: any) => {
  const {
    details,
    rating: { communication, professionalism, punctuality, positivity, helpfulness, dressCode },
  } = input;

  const finalData = {
    comment: details,
    rating: {
      communication: communication || 0,
      professionalism: professionalism || 0,
      punctuality: punctuality || 0,
      positivity: positivity || 0,
      helpfulness: helpfulness || 0,
      dress_code: dressCode || 0,
    },
  };

  try {
    const response = await HttpClient.post<any>(`contracts/${id}/reviews`, finalData);
    if (!response.error) {
      return response;
    }
  } catch (error) {
    console.error(error);
  }
};

export const jobReview = async ({ id, input }: any) => {
  const { details, shareWithSurely, shareWithClient } = input;

  const finalData = {
    comment: details,
    share_with_surely: shareWithSurely,
    share_with_client: shareWithClient,
  };

    const response = await HttpClient.post<any>(`contracts/${id}/reviews`, finalData);
    return response
};

export const askSuportClient = async ({ id, input }: any) => {
  const { document, details } = input;

  const finalData = {
    comment: details,
    files: document,
  };

  try {
    const response = await HttpClient.post<any>(`contracts/${id}/supports`, finalData);
    return response
    
  } catch (error) {
    console.error(error);
  }
};

export const askSuportOperator = async ({ id, input }: any) => {
  const { document, details } = input;

  const finalData = {
    comment: details,
    files: document,
  };

  try {
    const response = await HttpClient.post<any>(`contracts/${id}/supports`, finalData);
    return response; // Return the response regardless of error status
  } catch (error) {
    console.error(error);
    // Return the error response if available
    if (error.response && error.response.data) {
      return error.response.data;
    }
    // Otherwise return a generic error
    return { error: true, message: 'Failed to submit support request' };
  }
};

export const confirmContract = async (chatId: string, contractId: string) => {
  const url = `chats/${chatId}/contract/${contractId}/status`;
  const payload = {
    status: 'confirm-shifts',
    type: 'confirm-shifts',
  };

  try {
    const response = await HttpClient.post<any>(url, payload);
    if (!response.error) return response;
  } catch (error) {
    console.error(error);
  }
};

export let payEscrow = async (contractId: number, paymentMethodId: string) => {
  const url = `/contracts/${contractId}/pay-escrow`;
  const payload = {
    contract_id: contractId,
    payment_method_id: paymentMethodId,
  };

  try {
    const response = await HttpClient.post<any>(url, payload);
    return response;
  } catch (error) {
    console.error('Failed to pay escrow:', error);
    throw error;
  }
};

export const acceptContract = async (chatId: string, contractId: string) => {
  const url = `chats/${chatId}/contract/${contractId}/status`;
  const payload = {
    status: 'accept-shifts',
    type: 'accept-shifts',
  };

  try {
    const response = await HttpClient.post<any>(url, payload);
    if (!response.error) return response;
  } catch (error) {
    console.error(error);
  }
};

export const rejectContract = async (chatId: string, contractId: string) => {
  const url = `chats/${chatId}/contract/${contractId}/status`;
  const payload = {
    status: 'cancel',
    type: 'reject',
  };

  try {
    const response = await HttpClient.post<any>(url, payload);
    return response;
  } catch (error) {
    console.error(error);
  }
};

export const cancelContract = async (chatId: string, contractId: string, reason: string) => {
  const url = `chats/${chatId}/contract/${contractId}/status`;
  const payload = {
    status: 'cancel',
    type: 'cancel',
    reason,
    message: reason,
  };

  try {
    const response = await HttpClient.post<any>(url, payload);
    return response;
  } catch (error) {
    console.error(error);
  }
};

export const confirmShiftsContract = async (chatId: string, contractId: string) => {
  const url = `chats/${chatId}/contract/${contractId}/status`;
  const payload = {
    status: 'complete-shifts',
    type: 'complete-shifts',
  };

  try {
    const response = await HttpClient.post<any>(url, payload);
    if (!response.error) return response;
  } catch (error) {
    console.error(error);
  }
};

export const payOutstanding = async (contractId: number, paymentMethodId: string) => {
  const url = `/contracts/${contractId}/pay-escrow`;
  const payload = {
    contract_id: contractId,
    payment_method_id: paymentMethodId,
    is_outstanding: true
  };

  try {
    const response = await HttpClient.post<any>(url, payload);
    return response;
  } catch (error) {
    console.error('Failed to pay outstanding:', error);
    throw error;
  }
};

export const getPendingPayments = async () => {
  const url = `/contracts/payments`;
  try {
    const response = await HttpClient.get<any>(url);

    if (!response.error) return response;
  } catch (error) {
    console.error(error);
  }
};

export const getContractInvoice = async (id: any) => {
  const url = `/invoices/${id}`
  try {
    const response = await HttpClient.get<any>(url)
    return response
  } catch (err) {
    console.error(err)
  }
}

export const getAllInvoices = async () => {
  const url = `/invoices`
  try {
    const response = await HttpClient.get<any>(url)
    return response
  } catch (err) {
    console.error(err)
  }
}
