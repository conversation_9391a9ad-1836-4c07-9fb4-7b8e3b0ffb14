import React from 'react';
import laptopIcon from '../../../../assets/images/job-post-intro/laptop.png';

interface HireOptionButtonProps {
  isSelected: boolean;
  onClick: () => void;
}

const HireOptionButton: React.FC<HireOptionButtonProps> = ({ isSelected, onClick }) => {
  return (
    <button
      onClick={onClick}
      style={{
        width: '364px',
        height: '140px',
        backgroundColor: 'rgb(50, 61, 88)',
        border: isSelected ? '4px solid #3B82F6' : 'none',
        borderRadius: '12px',
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'flex-start',
        flexDirection: 'row',
        padding: '0 40px',
        gap: '30px',
        outline: 'none'
      }}
    >
      <img 
        src={laptopIcon} 
        alt="Looking to hire" 
        style={{
          width: '69.13px',
          height: '51.84px'
        }}
      />
      <span style={{
        color: 'rgb(255, 255, 255)',
        fontFamily: 'Rubik',
        fontSize: '20px',
        fontWeight: 700,
        lineHeight: '24px',
        letterSpacing: '0%',
        textAlign: 'left'
      }}>
        Looking to hire
      </span>
    </button>
  );
};

export default HireOptionButton;
