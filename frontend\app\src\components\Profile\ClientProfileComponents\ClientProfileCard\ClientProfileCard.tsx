// @ts-nocheck
import React, { useContext, useEffect, useState } from 'react';
import { Card, View, Divider, Image, Text, Icon, Button, useToggle } from 'reshaped';
import { IdVerified } from 'src/assets/icons';
import InviteToApplyModal from '../InviteToApplyModal/InviteToApplyModal';
import reporticon from '../../../../assets/icons/reporticon/reporticon.svg';
import '../../../../components/Header/HeaderMenu/HeaderMenu.css';
import inclusivitypledgeicon from '../../../../assets/icons/inclusivitypledgeicon/inclusivitypledgeicon.svg';
import NoDataClientProfile from '../NoDataClientProfile/NoDataClientProfile';
import { OperativesContext } from 'src/context/OperativesContext';
import { createChat } from 'src/services/chat';
import { useNavigate } from 'react-router-dom';
import { JobContext } from 'src/context/JobContext';
import PhotoAndVideoModal from '../../OperatorProfileComponents/PhotoAndVideoModal/PhotoAndVideoModal';
import { useAuthContext } from 'src/context/AuthContext';
import { useSearchParams } from 'react-router-dom';
import { useModalAction } from 'src/context/ModalContext';

interface ClientProfileCardProps {
  oper: any;
}

const getValidImageUrl = (image: string | undefined) => {
  if (!image?.includes('https://app.surelysecurity.com/storage/')) return 'https://app.surelysecurity.com/storage/' + image;
  return image;
};

const ClientProfileCard: React.FC<ClientProfileCardProps> = ({ oper }) => {
  const {
    name,
    city,
    sia_licence_types,
    additional_pictures,
    profile_photo,
    instant_book,
    profile_video,
    surely_pro_badge,
    sia_certificates,
    id,
    reviews,
    overall_ratings,
    reports_count,
    cv,
    jobs
  } = oper;

  const openCv = () => {
    let cvUrl = cv;
    const baseUrl = 'https://app.surelysecurity.com/storage/';
    if (!cv.startsWith(baseUrl)) {
      cvUrl = baseUrl + cv;
    }
    window.open(cvUrl, '_blank');
  };

  const { allOperatives, handleLoadMore, handleSelectedOperatives, addFavorite, removeFavorite, fetchAllOperative } = useContext(OperativesContext);
  const { fetchAllJobs, allJobs } = useContext(JobContext);

  const navigate = useNavigate();
  const is_favorite = oper?.is_favorite;

  const { active: active1, activate: activate1, deactivate: deactivate1 } = useToggle(false);
  const lastname = name?.split(' ').pop()?.charAt(0).toUpperCase() + name?.split(' ').pop()?.slice(1);
  const firstname = name?.split(' ').shift()?.charAt(0).toUpperCase() + name?.split(' ').shift()?.slice(1);
  const initialName = `${firstname || ''} ${lastname || ''}`;

  const { user } = useAuthContext();
  const accountType = user?.profile ? String(user.profile.account_type) : String(user?.account_type);
  const isGuest = accountType === '5';

  const [searchParams, setSearchParams] = useSearchParams();
  const { user: userInfo, isAuthenticated } = useAuthContext();
  const { openModal } = useModalAction();
  
  useEffect(() => {
    fetchAllJobs();
  }, []);

  const onlycity = city?.split(', ', 1).pop();

  const [isFavourite, setIsFavourite] = useState(is_favorite);
  const [modalActive, setModalActive] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState<string[] | null>(null);
  const [currentMediaIndex, setCurrentMediaIndex] = useState(0);

  const openModal2 = (mediaList: string[], index: number) => {
    setSelectedMedia(mediaList);
    setCurrentMediaIndex(index);
    setModalActive(true);
  };

  const handleNext = () => {
    if (selectedMedia && currentMediaIndex < selectedMedia.length - 1) {
      setCurrentMediaIndex(currentMediaIndex + 1);
    }
  };

  const handlePrevious = () => {
    if (selectedMedia && currentMediaIndex > 0) {
      setCurrentMediaIndex(currentMediaIndex - 1);
    }
  };

  const toggleFavorites = () => {
    setIsFavourite((prevFavourite: any) => !prevFavourite);
    if (isFavourite) {
      removeFavorite(id);
    } else {
      addFavorite(id);
    }
  };

  const handleChatClick = () => {
    if (!isAuthenticated) {
      openModal('REGISTER');
      return;
    }
    if (isGuest) {
      openModal('GUEST_UPGRADE');
      return;
    }
    createChat(id, `Start your conversation with ${initialName}`, 'no_contract').then((res) => {
      navigate(`/chat`, { state: { operator_id: id } });
    });
  };

  const handleInviteClick = () => {
    if (!isAuthenticated) {
      openModal('REGISTER');
      return;
    }
    if (isGuest) {
      openModal('GUEST_UPGRADE');
      return;
    }
    activate1();
  };

  return (
    <View className='lg:mx-auto'>
      <Card className='flex h-auto  flex-col justify-between p-6 xl:w-[424px]'>
        <View className='m-[12px]'>
          <View className='flex items-center justify-between gap-2'>
            <div className='flex items-center'>
              {sia_certificates?.[0]?.sia_licence === 1 ? (
                <span className='rubik mr-[5px] h-[auto] w-[auto] rounded-md !bg-[#E6FEF3] p-1 text-xs font-normal leading-4 text-[#05751F]'>
                  SIA CERTIFIED
                </span>
              ) : (
                <span className='rubik mr-[5px] h-[auto] w-[auto] rounded-md !bg-[#EDEDED] p-1 text-xs font-normal leading-4 text-[#6A6A6A]'>
                  SIA PENDING
                </span>
              )}
              {instant_book ? (
                <span className='rubik mr-[5px] w-[auto] rounded-md !bg-[#D1E1FF] px-[8px] py-1 text-[12px] font-normal leading-4 text-[#0D2F87]'>
                  INSTANT BOOK
                </span>
              ) : (
                <div />
              )}
              {reports_count === 0 ? (
                <button className='inline-flex h-[24px] w-[44px] items-center justify-center gap-1 rounded-[999px] border border-[#323C58] bg-white px-2 py-1'>
                  <div className='inline-flex items-center justify-center'>
                    <span className='material-icons-outlined mt-[2px] text-[17px] text-[#323C58]'>report</span>
                  </div>
                  <div className='rubik mt-[2px] text-[12px] font-medium leading-tight text-[#323C58]'>0</div>
                </button>
              ) : reports_count === 1 || reports_count === 2 ? (
                <button className='ml-2 inline-flex h-[24px] w-[44px] items-center justify-center gap-1 rounded-[999px] border border-[#FEC373] bg-[#FEC373] px-2 py-1'>
                  <div className='inline-flex items-center justify-center'>
                    <span className='material-icons-outlined mt-[2px] text-[17px] text-[#DF8204]'>report</span>
                  </div>
                  <div className='rubik mt-[2px] mt-[2px] text-[12px] font-medium leading-tight text-[#DF8204]'>{reports_count}</div>
                </button>
              ) : (
                <button className='inline-flex h-[24px] w-[44px] items-center justify-center gap-1 rounded-[999px] border border-[#FBD0D4] bg-[#FBD0D4] px-2 py-1'>
                  <div className='inline-flex items-center justify-center'>
                    <span className='material-icons-outlined mt-[2px] text-[17px] text-[#CB101D]'>report</span>
                  </div>
                  <div className='rubik mt-[2px] mt-[2px] text-[12px] font-medium leading-tight text-[#CB101D]'>{reports_count}</div>
                </button>
              )}
            </div>
          </View>

          <View className='mt-[7px] flex flex-row items-center gap-4'>
            <View>
              {profile_photo ? (
                <div
                  className='rounded-full p-0.5'
                  style={{
                    background: 'linear-gradient(180deg, rgba(50, 60, 88, 0.855), rgba(113, 225, 248, 0.9025))',
                  }}
                >
                  <Image
                    className='flex h-[80px] w-[80px] flex-col items-start rounded-full bg-white'
                    alt='Profile'
                    src={getValidImageUrl(profile_photo)}
                  />
                </div>
              ) : (
                <View className='flex h-[80px]  w-[80px] items-center justify-center  rounded-full bg-[#0B80E7]'>
                  <Text className='rubik text-[30px] text-white'>{name?.charAt(0)}</Text>
                </View>
              )}
              <Icon className='absolute bottom-0 right-0 h-[31px] w-[30px]' svg={IdVerified} />
            </View>
            <View className='flex flex-col justify-start gap-1'>
              {overall_ratings?.count > 0 && (
                <View className='flex items-center gap-2'>
                  <span className='material-icons text-[#F4BF00]'>star</span>
                  <Text className='rubik text-[15px] font-medium leading-[20px] text-[#323c58]'>
                    {overall_ratings.value} ({Math.round(overall_ratings.value)})
                  </Text>
                </View>
              )}
              <View>
                <Text className='rubik font-medium text-[#1A1A1A]'>{initialName}</Text>
              </View>
              <View className='flex items-center'>
                {city ? (
                  <>
                    <Text className='rubik text-line-height text-[16px] leading-5   text-blue-400 '>{onlycity}</Text>
                    <span className='material-icons-outlined icon-line-height text-[15px] text-blue-400'>place</span>
                  </>
                ) : (
                  <></>
                )}
              </View>
            </View>
          </View>
          {/* <View className='flex flex-col items-start'>
            <Text className='rubik mr-[0px] mt-[22px] font-medium text-[#1A1A1A]'>Jobs completed: {jobs?.completed_jobs}</Text>
            <Divider className='mt-[10px] h-[1px] w-full' />
          </View> */}
          <View className='mt-[20px] flex flex-wrap gap-2 '>
            {sia_licence_types?.map((grade: any, index: any) => (
              <Button
                size='small'
                key={index}
                rounded={true}
                elevated={false}
                className='max-w-xs overflow-hidden truncate border border-[#05751F] !bg-[#E6FEF3] px-2 py-1 text-xs text-[#323c58]'
              >
                <Text color='positive' className='rubik flex items-center gap-1'>
                  <span className='material-icons text-[16px]'>star</span>
                  {grade}
                </Text>
              </Button>
            ))}
            {sia_certificates?.[0]?.sia_licence === 1 &&
              sia_certificates?.[0]?.id_check === 1 &&
              sia_certificates?.[0]?.employment_history === 1 &&
              sia_certificates?.[0]?.no_criminal_record === 1 &&
              sia_certificates?.[0]?.credit_check === 1 &&
              sia_certificates?.[0]?.proof_of_address === 1 && (
                <View className='gap-2'>
                  <Button
                    size='small'
                    rounded={true}
                    elevated={false}
                    className='mr-[5px] max-w-xs overflow-hidden  truncate border !bg-[#7CDBEF] px-2 py-1 text-xs text-[#323c58] '
                  >
                    <Text className='rubik flex items-center gap-1 text-[12px] font-medium text-[#323c58]'>
                      <span className='material-icons text-[16px] text-[#323c58]'>security</span>
                      BS7858
                    </Text>
                  </Button>
                </View>
              )}
            {surely_pro_badge?.some((badge: any) => badge.type === 'InclusivityPledge') && (
              <Button
                size='small'
                variant='outline'
                className='border-dark-gradient flex w-[170px] items-center justify-center gap-1 self-stretch rounded-full border !bg-[#ffff] '
                icon={() => <img src={inclusivitypledgeicon} className='w-[20px]' />}
              >
                <Text className='rubik text-[12px] font-medium not-italic leading-5 '>Inclusivity Pledge</Text>
              </Button>
            )}
          </View>
        </View>
      </Card>

      <Button
        onClick={handleChatClick}
        className='border-neutral bg-background-base mt-[12px] flex h-[48px] w-full items-center justify-center gap-2 self-stretch self-stretch rounded-[8px] border !bg-[#0B80E7] px-4 py-2 lg:mt-[24px]'
      >
        <Text className='rubik text-[16px] font-medium leading-[24px] text-[#fff]'>Chat</Text>
      </Button>
      <Button
        onClick={handleInviteClick}
        className='border-neutral bg-background-base mt-[12px] flex h-[48px] w-full items-center justify-center gap-2 self-stretch self-stretch rounded-[8px] border !bg-[white] px-4 py-2 !text-[#0B80E7] shadow-md lg:mt-[12px]'
      >
        <Text className='rubik text-[16px] font-medium leading-[24px] text-[#388DD8]'>Invite to apply</Text>
      </Button>

      <InviteToApplyModal allJobs={allJobs} operatorId={oper?.id} active={active1} deactivate={deactivate1} />
      {!isGuest && (
        <Button
          onClick={toggleFavorites}
          className='btn-no-hover mt-[12px] flex h-[48px] w-full items-center justify-center gap-2 self-stretch self-stretch !bg-transparent px-4 py-2 !text-[#0B80E7] lg:mt-[12px]'
          fullWidth={true}
        >
          {!isFavourite && (
            <View className='flex flex-row items-center justify-center gap-2'>
              <svg xmlns='http://www.w3.org/2000/svg' width='15' height='14' viewBox='0 0 15 14' fill='none'>
                <path d='M10.6634 0.667969C9.4193 0.667969 8.22525 1.24712 7.4459 2.16232C6.66655 1.24712 5.4725 0.667969 4.2284 0.667969C2.0262 0.667969 0.295898 2.39827 0.295898 4.60047C0.295898 7.30317 2.7269 9.50537 6.40915 12.8516L7.4459 13.7882L8.48265 12.8444C12.1649 9.50537 14.5959 7.30317 14.5959 4.60047C14.5959 2.39827 12.8656 0.667969 10.6634 0.667969ZM7.5174 11.7862L7.4459 11.8577L7.3744 11.7862C3.971 8.70457 1.7259 6.66682 1.7259 4.60047C1.7259 3.17047 2.7984 2.09797 4.2284 2.09797C5.3295 2.09797 6.402 2.80582 6.78095 3.78537H8.118C8.4898 2.80582 9.5623 2.09797 10.6634 2.09797C12.0934 2.09797 13.1659 3.17047 13.1659 4.60047C13.1659 6.66682 10.9208 8.70457 7.5174 11.7862Z' fill='#0B80E7'/>
              </svg>
              <Text className='rubik text-[16px] font-medium leading-[24px] text-[#388DD8]'>Add to favourites</Text>
            </View>
          )}
          {isFavourite && (
            <View className='flex flex-row items-center justify-center gap-2'>
              <svg xmlns='http://www.w3.org/2000/svg' width='15' height='14' viewBox='0 0 15 14' fill='none'>
                <path d='M10.6634 0.667969C9.4193 0.667969 8.22525 1.24712 7.4459 2.16232C6.66655 1.24712 5.4725 0.667969 4.2284 0.667969C2.0262 0.667969 0.295898 2.39827 0.295898 4.60047C0.295898 7.30317 2.7269 9.50537 6.40915 12.8516L7.4459 13.7882L8.48265 12.8444C12.1649 9.50537 14.5959 7.30317 14.5959 4.60047C14.5959 2.39827 12.8656 0.667969 10.6634 0.667969ZM7.5174 11.7862L7.4459 11.8577L7.3744 11.7862C3.971 8.70457 1.7259 6.66682 1.7259 4.60047C1.7259 3.17047 2.7984 2.09797 4.2284 2.09797C5.3295 2.09797 6.402 2.80582 6.78095 3.78537H8.118C8.4898 2.80582 9.5623 2.09797 10.6634 2.09797C12.0934 2.09797 13.1659 3.17047 13.1659 4.60047C13.1659 6.66682 10.9208 8.70457 7.5174 11.7862Z' fill='#CB3E59'/>
              </svg>
              <Text className='rubik text-[16px] font-medium leading-[24px] text-[#CB3E59]'>Remove from favourites</Text>
            </View>
          )}
        </Button>
      )}
      {cv && isAuthenticated && !isGuest && (
        <Button
          onClick={openCv}
          className='btn-no-hover flex h-[48px] w-full items-center justify-center gap-2 self-stretch self-stretch  !bg-transparent  px-4 py-2 !text-[#0B80E7]  lg:mt-[12px]'
          fullWidth={true}
        >
          <View className='flex flex-row items-center justify-center gap-2 '>
            <span className='material-icons-outlined text-[19px]'>contact_page</span>
            <Text className='rubik text-[16px] font-medium leading-[24px] text-[#388DD8]'>Curriculum Vitae</Text>
          </View>
        </Button>
      )}

      <Card className='mt-[24px]  h-[424px] flex-wrap p-6 xl:w-[424px]'>
        {additional_pictures?.length > 0 || profile_video ? (
          <View className='grid grid-cols-2 gap-x-[12px] gap-y-[12px]'>
            {additional_pictures?.map((photo: any, index: any) => (
              <img
                key={index}
                className='h-[182px] w-[182px] cursor-pointer overflow-hidden rounded-lg object-cover '
                src={`https://app.surelysecurity.com/storage/${photo}`}
                onClick={() => openModal2(additional_pictures, index)}
              />
            ))}
            {profile_video && (
              <video
                src={profile_video}
                controls
                className='h-[182px] w-[182px] cursor-pointer overflow-hidden rounded-lg object-cover  '
                onClick={() => openModal2([profile_video], 0)}
              />
            )}
          </View>
        ) : (
          <div className='mt-[25%]'>
            <NoDataClientProfile />
          </div>
        )}
      </Card>

      <PhotoAndVideoModal
        active={modalActive}
        deactivate={() => {
          setSelectedMedia(null);
          setCurrentMediaIndex(0);
          setModalActive(false);
        }}
        mediaList={selectedMedia}
        currentMediaIndex={currentMediaIndex}
        onNext={handleNext}
        onPrevious={handlePrevious}
      />
    </View>
  );
};

export default ClientProfileCard;
