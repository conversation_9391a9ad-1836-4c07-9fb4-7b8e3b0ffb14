[2025-08-10 11:22:33] local.DEBUG: Message-ID: <0d9ab577e024d2150fbf46350e72bcdb@127.0.0.1>
Date: Sun, 10 Aug 2025 11:22:33 +0000
Subject: Verify Your Surely Email
From: Surely <<EMAIL>>
To: <EMAIL>
MIME-Version: 1.0
Content-Type: text/html; charset=utf-8
Content-Transfer-Encoding: quoted-printable

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Your Surely App Account</title>
</head>
<body>
    <p>Dear nojeme 6798,</p>


    <p>Thank you for choosing Surely! We're excited to have you on board and want to ensure the security of your account. To complete the registration process and gain full access to our features, please verify your email address.</p>


    <p>To verify your email, simply click on the link below:</p>


    <a href="http://localhost:5173/verify-email?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzM4NCJ9.******************************************************.Frh9gSYE_bypGUK19oCqZRd73-i-ciajaIaRpINByDAb1o-gqUnjGXEbZALp31_D">Click here to verify your email</a>


    <p>If you are unable to click the link, please copy and paste it into your browser's address bar.</p>


    <p>Please note that this link is valid for the next 24 hours. After this period, you may need to request a new verification email.</p>


    <p>Thanks once again for your support. It really does make all the difference.</p>


    <p>Kind regards,<br>
    Aliona & Aurela</p>
</body>
</html>
  
[2025-08-10 11:22:33] local.INFO: Verification email sent successfully {"user_id":3067,"email":"<EMAIL>","token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzM4NCJ9.******************************************************.Frh9gSYE_bypGUK19oCqZRd73-i-ciajaIaRpINByDAb1o-gqUnjGXEbZALp31_D"} 
[2025-08-10 11:22:47] local.INFO: verify_email function called.  
[2025-08-10 11:22:47] local.INFO: Token retrieved from request. {"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzM4NCJ9.******************************************************.Frh9gSYE_bypGUK19oCqZRd73-i-ciajaIaRpINByDAb1o-gqUnjGXEbZALp31_D"} 
[2025-08-10 11:22:47] local.INFO: Attempting to decode token.  
[2025-08-10 11:22:47] local.INFO: Token successfully decoded. {"decoded_data":{"user_id":3067,"expires_at":1754828553}} 
[2025-08-10 11:22:47] local.INFO: Fetched user from database. {"user_id":3067,"user_email_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzM4NCJ9.******************************************************.Frh9gSYE_bypGUK19oCqZRd73-i-ciajaIaRpINByDAb1o-gqUnjGXEbZALp31_D"} 
[2025-08-10 11:22:47] local.INFO: Creating new access token for user.  
[2025-08-10 11:22:47] local.INFO: User email verified and token cleared. {"user_id":3067} 
[2025-08-10 11:22:47] local.INFO: Verification process completed successfully. {"error":false,"message":"Success","token":"************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************","data":{"id":3067,"name":"nojeme 6798","email":"<EMAIL>","email_verified":true,"phone":"**********","account_type":"1","age":0,"height":"","gender":"","pay_rate":0,"years_of_experience":0,"company_registration":"","expertise_id":0,"expertise_name":"","address1":"","address2":"","address_1":"London","address_2":"","postal_code":"W1A 1AA","website":"","city_id":0,"city":"Not Specified","lat":"51.518563","lng":"-0.143774","language":null}} 
[2025-08-10 11:34:04] local.INFO: Broadcasting [App\Events\NewApplicationEvent] on channels [surely-development] with payload:
{
    "application": {
        "operator_name": "Leo Bianchi",
        "job_id": 309,
        "post_name": "Roje te dera e Tahirit",
        "proposition_details": "test",
        "receiver_id": 822,
        "sender_id": 823
    },
    "socket": null
}  
