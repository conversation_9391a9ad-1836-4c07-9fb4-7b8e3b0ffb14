// @ts-nocheck
import React, { useState, useEffect } from 'react';
import { Image, Text, View, Button, Divider, Modal, TextField } from 'reshaped';
import { useToastSystem } from 'src/context/ToastSystemContext';
import '../../../../components/Header/HeaderMenu/HeaderMenu.css';
import { getPaymentDetails, getBankCardDetails, addBankCardDetails, } from 'src/services/settings';
import surleyicon from '../../../../assets/icons/surleyicon/surleyicon.png';
import { loadStripe } from '@stripe/stripe-js';
import { Elements, CardElement, useStripe, useElements } from '@stripe/react-stripe-js';
import { ChatService } from 'src/services/chat';
import { getErrorMessage } from 'src/utils/errorHandler';

interface PaymentModalProps {
  active: boolean;
  deactivate: () => void;
  payEscrow: (cardId: number) => any;
  payOutstanding: (cardId: number) => any;
  contract: any;
  isOutstanding?: boolean;
  loadContract: (id: string | number) => Promise<any>;
  handleGetChats: () => Promise<any>;
  chatId: number;
}

const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY, {
  stripeAccount: import.meta.env.VITE_STRIPE_ACCOUNT_ID
});

interface PaymentFormProps {
  payAmount: number;
  onSuccess: () => void;
  onError: (message: string) => void;
  contract: any;
  payEscrow: (id: number, paymentMethodId: string) => Promise<any>;
  payOutstanding: (id: number, paymentMethodId: string) => Promise<any>;
  isOutstanding: boolean;
  loadContract: (id: string | number) => Promise<any>;
  handleGetChats: () => Promise<any>;
}

const PaymentForm: React.FC<PaymentFormProps> = ({ payAmount, onSuccess, onError, contract, payEscrow, payOutstanding, isOutstanding, loadContract, handleGetChats }) => {
  const stripe = useStripe();
  const elements = useElements();
  const toastSystem = useToastSystem();
  const [savedCards, setSavedCards] = useState([]);
  const [selectedCard, setSelectedCard] = useState(null);
  const [showNewCard, setShowNewCard] = useState(false);

  useEffect(() => {
    loadSavedCards();
  }, []);

  const loadSavedCards = async () => {
    try {
      const response = await getBankCardDetails();
      if (response?.data?.length) {
        setSavedCards(response.data);
        setSelectedCard(response.data[0]?.id);
        setShowNewCard(false);
      } else {
        setShowNewCard(true);
      }
    } catch (error) {
      console.error('Failed to load saved cards:', error);
      setShowNewCard(true);
    }
  };

  const handleSubmit = async (event) => {
    event.preventDefault();

    if (!stripe || (!elements && !selectedCard)) {
      return;
    }

    try {
      let paymentMethodId;

      if (showNewCard) {
        // Handle new card payment
        const { error, paymentMethod } = await stripe.createPaymentMethod({
          type: 'card',
          card: elements?.getElement(CardElement),
        });

        if (error) {
          throw new Error(error.message);
        }
        paymentMethodId = paymentMethod.id;

        // Save the new card first
        const saveResponse = await addBankCardDetails({
          payment_method_id: paymentMethodId,
          holder_name: 'Card Holder'
        });

        if (saveResponse?.error) {
          throw new Error(saveResponse.message || 'Failed to save card');
        }
      } else {
        // Use existing card
        if (!selectedCard) {
          throw new Error('Please select a card');
        }
        let selectedCardData = savedCards.find(card => card.id === parseInt(selectedCard));
        paymentMethodId = selectedCardData?.payment_method_id;
      }

      // Call appropriate payment function based on isOutstanding flag
      const response = isOutstanding 
        ? await payOutstanding(contract.id, paymentMethodId)
        : await payEscrow(contract.id, paymentMethodId);
      
      if (!response || response.error) {
        throw new Error(response?.message || 'Payment failed');
      }

      // After successful payment, update the contract and chat data
      await Promise.all([
        loadContract(contract.id),
        handleGetChats()
      ]);

      onSuccess();
    } catch (err) {
      const errorMessage = getErrorMessage(err);
      onError(errorMessage);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      {savedCards.length > 0 ? (
        <View className='mb-4'>
          <Text className='rubik mb-2 text-[14px] font-medium'>Saved Cards</Text>
          {!showNewCard && (
            <>
              <select
                value={selectedCard || ''}
                onChange={(e) => {
                  setSelectedCard(e.target.value);
                  setShowNewCard(false);
                }}
                className='w-full rounded border p-2'
              >
                {savedCards.map(card => (
                  <option key={card.id} value={card.id}>
                    **** **** **** {card.last_numbers}
                  </option>
                ))}
              </select>
              <Button
                type="button"
                onClick={() => {
                  setShowNewCard(true);
                  setSelectedCard(null);
                }}
                className='mt-2 text-blue-600'
              >
                Use New Card
              </Button>
            </>
          )}
        </View>
      ) : (
        !showNewCard && (
          <View className='mb-4'>
            <Text className='rubik mb-2 text-[14px] font-medium'>No saved cards</Text>
            <Button
              type="button"
              onClick={() => {
                setShowNewCard(true);
                setSelectedCard(null);
              }}
              className='text-blue-600'
            >
              Add New Card
            </Button>
          </View>
        )
      )}

      {showNewCard && (
        <View className='mt-[16px] flex flex-col gap-[4px]'>
          <Text className='rubik text-[14px] font-medium leading-[20px] text-[#14171F]'>
            {savedCards.length > 0 ? 'New Card Details' : 'Add Card Details'}
          </Text>
          <CardElement
            options={{
              style: {
                base: {
                  fontSize: '16px',
                  color: '#424770',
                  '::placeholder': {
                    color: '#aab7c4',
                  },
                },
                invalid: {
                  color: '#9e2146',
                },
              },
            }}
          />
          {savedCards.length > 0 && (
            <Button
              type="button"
              onClick={() => {
                setShowNewCard(false);
                setSelectedCard(savedCards[0]?.id);
              }}
              className='mt-2 text-blue-600'
            >
              Back to Saved Cards
            </Button>
          )}
        </View>
      )}

      <Divider className='mt-[16px] h-[1px] w-full' />
      
      <View className='mt-[16px] flex flex-row justify-end'>
        <Button
          type="submit"
          disabled={!stripe || (!selectedCard && !showNewCard)}
          className='border-neutral flex h-[48px] w-[180px] items-center justify-center gap-2 rounded-[8px] border !bg-[#0B80E7] px-4 py-2'
        >
          <Text className='rubik text-[16px] font-medium leading-[24px] text-[#FFFFFF]'>
            Pay £{payAmount}
          </Text>
        </Button>
      </View>
    </form>
  );
};

const PaymentModal: React.FC<PaymentModalProps> = ({ active, deactivate, payEscrow, payOutstanding, contract, isOutstanding, loadContract, handleGetChats, chatId }) => {
  const toastSystem = useToastSystem();

  const handleSuccess = async () => {
    try {
      // Send system message about payment
      if (chatId) {
        const messageText = isOutstanding 
          ? `Outstanding payment of £${contract?.pay_amount} has been completed.`
          : `Escrow payment of £${contract?.escrow_amount} has been added to the contract.`;
          
        await ChatService.sendMessage(chatId, {
          message: messageText,
          type: 'payment_status',
          metadata: {
            payment_type: isOutstanding ? 'outstanding' : 'escrow',
            amount: isOutstanding ? contract?.pay_amount : contract?.escrow_amount,
            status: 'paid'
          }
        });
      }

      // Show success toast
      toastSystem.showSuccess(
        `${isOutstanding ? 'Outstanding payment' : 'Escrow payment'} successful!`,
        ''
      );
      
      deactivate();
    } catch (error) {
      console.error('Error sending payment message:', error);
    }
  };

  const handleError = async (message) => {
    if (chatId) {
      const messageText = isOutstanding 
        ? `Outstanding payment of £${contract?.pay_amount} failed.`
        : `Escrow payment of £${contract?.escrow_amount} failed.`;
        
      await ChatService.sendMessage(chatId, {
        message: messageText,
        type: 'payment_status',
        metadata: {
          payment_type: isOutstanding ? 'outstanding' : 'escrow',
          amount: isOutstanding ? contract?.pay_amount : contract?.escrow_amount,
          status: 'failed'
        }
      });
    }

    toastSystem.showError('Payment failed', message);
  };

  return (
    <Modal active={active} onClose={deactivate} className='!h-[auto] !w-[424px] p-[24px]'>
      <Elements stripe={stripePromise}>
        <PaymentForm 
          payAmount={isOutstanding ? contract?.pay_amount : contract?.escrow_amount}
          onSuccess={handleSuccess}
          onError={handleError}
          contract={contract}
          payEscrow={payEscrow}
          payOutstanding={payOutstanding}
          isOutstanding={isOutstanding}
          loadContract={loadContract}
          handleGetChats={handleGetChats}
        />
      </Elements>
    </Modal>
  );
};

export default PaymentModal;
