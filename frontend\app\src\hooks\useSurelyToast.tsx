// @ts-nocheck
import React, { useState, useCallback } from 'react';
import SurelyToast, { SurelyToastProps } from '../components/Toast/SurelyToast';

export interface ToastConfig {
  /** The title of the toast */
  title: string;
  /** The main content/message of the toast */
  content: string;
  /** Optional button configuration */
  button?: {
    text: string;
    onClick: () => void;
  };
  /** Whether to show the Surely icon */
  showIcon?: boolean;
  /** Additional CSS classes for positioning */
  className?: string;
  /** Auto-hide timeout in milliseconds (0 = no auto-hide) */
  timeout?: number;
  /** Optional deduplication key. If provided, multiple toasts with the same key won't duplicate. */
  key?: string;
  /** Enable/disable deduplication (default: true) */
  dedupe?: boolean;
}

export interface ToastState {
  id: string;
  config: ToastConfig;
  isVisible: boolean;
  /** Internal computed key used for deduplication */
  _dedupeKey?: string;
}

const useSurelyToast = () => {
  const [toasts, setToasts] = useState<ToastState[]>([]);

  const hide = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  const hideAll = useCallback(() => {
    setToasts([]);
  }, []);


  const show = useCallback((config: ToastConfig): string => {
    const id = Math.random().toString(36).substr(2, 9);

    // Compute a dedupe key based on title/content/button text unless provided
    const computedKey = config.key || `${config.title}::${config.content}::${config.button?.text || ''}`;
    const dedupeEnabled = config.dedupe !== false; // default true

    setToasts(prev => {
      // Prevent duplicates with the same key when dedupe is enabled
      if (dedupeEnabled && prev.some(t => (t._dedupeKey || '') === computedKey)) {
        return prev; // do not add duplicate
      }

      const newToast: ToastState = {
        id,
        config,
        isVisible: true,
        _dedupeKey: computedKey,
      };
      return [...prev, newToast];
    });

    // Auto-hide if timeout is specified
    if (config.timeout && config.timeout > 0) {
      setTimeout(() => {
        hide(id);
      }, config.timeout);
    }

    return id;
  }, [hide]);

  // Predefined toast types for common use cases
  const showSuccess = useCallback((title: string, content: string, button?: ToastConfig['button']) => {
    return show({
      title,
      content,
      button,
      showIcon: true,
      timeout: 5000, // Auto-hide after 5 seconds
      key: `success::${title}::${content}::${button?.text || ''}`,
    });
  }, [show]);

  const showError = useCallback((title: string, content: string, button?: ToastConfig['button']) => {
    return show({
      title,
      content,
      button,
      showIcon: true,
      timeout: 0, // Don't auto-hide errors
      key: `error::${title}::${content}::${button?.text || ''}`,
    });
  }, [show]);

  const showInfo = useCallback((title: string, content: string, button?: ToastConfig['button']) => {
    return show({
      title,
      content,
      button,
      showIcon: true,
      timeout: 8000, // Auto-hide after 8 seconds
      key: `info::${title}::${content}::${button?.text || ''}`,
    });
  }, [show]);

  const showWelcome = useCallback((content: string, button?: ToastConfig['button']) => {
    return show({
      title: 'Welcome to our platform!',
      content,
      button,
      showIcon: true,
      timeout: 0, // Don't auto-hide welcome messages
      key: `welcome::${content}::${button?.text || ''}`,
    });
  }, [show]);

  // Render function to display all active toasts
  const renderToasts = useCallback(() => {
    // Render a single fixed container that stacks toasts vertically without overlap
    return (
      <div className="fixed bottom-1 sm:bottom-7 right-[15.3%] sm:right-5 z-50 flex flex-col space-y-3">
        {toasts.map((toast) => (
          <SurelyToast
            key={toast.id}
            title={toast.config.title}
            content={toast.config.content}
            button={toast.config.button}
            onClose={() => hide(toast.id)}
            isVisible={toast.isVisible}
            showIcon={toast.config.showIcon}
            fixed={false}
          />
        ))}
      </div>
    );
  }, [toasts, hide]);

  return {
    show,
    hide,
    hideAll,
    showSuccess,
    showError,
    showInfo,
    showWelcome,
    renderToasts,
    toasts,
  };
};

export default useSurelyToast;
