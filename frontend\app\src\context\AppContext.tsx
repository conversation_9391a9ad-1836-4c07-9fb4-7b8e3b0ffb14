// @ts-nocheck
import React, { createContext, useContext, useEffect, useState } from 'react';
import { getBankAccount, getBankCardDetails, getNotifications, getPosition, getProfileDetails, getSurelyProBadge } from 'src/services/settings';
import { getProfileOperatorData } from 'src/services/user';
import { getAllFavouriteJobs, toggleFavourite } from 'src/services/favourites';
import { getSiaLicence } from 'src/services/settings';
import { AuthContext } from './AuthContext';

export const AppContext = createContext<any>(null);
interface LanguageData {
  language: any;
  level: any;
}

export const AppProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isClient, isAuthenticated } = useContext(AuthContext);
  const [profileTitle, setProfileTitle] = useState<string | null>(null);
  const [profileDescription, setProfileDescription] = useState<string | null>(null);
  const [positions, setPositions] = useState<string[]>([]);
  const [qualifications, setQualifications] = useState<string[]>([]);
  const [surelyProBadges, setSurelyProBadges] = useState([]);
  const [operatorId, setOperatorId] = useState<string>();
  const [name, setName] = useState<string>();
  const [cityInfo, setCityInfo] = useState<string>();
  const [email, setEmail] = useState<string>();
  const [phoneNumber, setPhoneNumber] = useState<string>();
  const [postalCity, setPostalCity] = useState<string>();
  const [postalCode, setPostalCode] = useState<string>();
  const [addressLine, setAddressLine] = useState<string>();
  const [county, setCounty] = useState<string>();
  const [locationRange, setlocationRange] = useState();
  const [profilePhoto, setProfilePhoto] = useState<string | undefined>();
  const [profileVideo, setProfileVideo] = useState<string | undefined>();
  const [siaLicense, setSiaLicense] = useState([]);
  const [additionalPictures, setAdditionalPictures] = useState<string[]>([]);
  const [instantBook, setInstantBook] = useState<boolean>(false);
  const [siaLicence, setSiaLicence] = useState();
  const [idCheck, setIdCheck] = useState();
  const [proofOfAddress, setProofOfAddress] = useState();
  const [employmentHistory, setEmploymentHistory] = useState();
  const [creditCheck, setCreditCheck] = useState();
  const [noCriminalRecord, setNoCriminalRecord] = useState();
  const [industrySectors, setIndustrySectors] = useState<string[]>([]);
  const [testlang, settestlang] = useState<any>([]);
  const [languages, setLanguages] = useState<LanguageData[]>([]);
  const [addressVerificationDocument, setAddressVerificationDocument] = useState();
  const [documentType, setDocumentType] = useState();
  const [idBackDocument, setIdBackDocument] = useState();
  const [idFrontDocument, setIdFrontDocument] = useState();
  const [selfieVerificationDocument, setSelfieVerificationDocument] = useState();
  const [siaLicenceCardPhoto, setSiaLicenceCardPhoto] = useState();
  const [siaLicenceExpiryDate, setSiaLicenceExpiryDate] = useState();
  const [siaLicenceNumber, setSiaLicenceNumber] = useState();
  const [latMap, setLatMap] = useState();
  const [lngMap, setLngMap] = useState();
  const [jobDataOperator, setJobDataOperator] = useState();

  const [completenessValue, setCompletenessValue] = useState<number>(10);
  const [clientRepresentBusinessAs, setClientRepresentBusinessAs] = useState(undefined);
  const [companyRegisteredNumber, setCompanyRegisteredNumber] = useState(undefined);

  const [favouriteJobs, setFavouriteJobs] = useState<any>([]);
  const [favouriteOperators, setFavouriteOperators] = useState<any>([]);

  const [hasCreditCard, setHasCreditCard] = useState();
  const [hasBankAccount, setHasBankAccount] = useState();

  const [otherReview, setOtherReview] = useState<any>();
  const [overallReview, setOverallReview] = useState<any>();

  const [reportCount, setReportCount] = useState<any>();

  const [notificationSettings, setNotificationSettings] = useState({});

  const toggleFavouriteJob = (id: number) => toggleFavourite(id);
  const toggleFavouriteOperator = (id: number) => toggleFavourite(id);

  const handleGetSurelyProBadges = () => {
    getSurelyProBadge().then((data: any) => {
      setSurelyProBadges(data?.data || []);
    });
  };

  const handleGetPositions = () => {
    getPosition().then((data: any) => {
      setPositions(data?.employments || '');
      setQualifications(data?.qualifications || '');
    });
  };

  const handleGetProfileDetails = () => {
    getProfileDetails().then((data: any) => {
      setSiaLicense(data?.SIALicense || []);
    });
  };

  const handleGetNotificationSettings = () => {
    getNotifications().then((data: any) => {
      setNotificationSettings(data?.data);
    });
  };

  const handleGetProfileOperatorData = () => {
    getProfileOperatorData().then((data: any) => {
      setJobDataOperator(data?.jobs);
      setOperatorId(data?.id);
      setOtherReview(data?.otherReview);
      setOverallReview(data?.overallReview);
      setProfileTitle(data?.profileTitle);
      setProfileDescription(data?.profileDescription);
      setIndustrySectors(data?.industrySectors || []);
      setDocumentType(data?.documentType);
      setAddressVerificationDocument(data?.addressVerificationDocument);
      setIdBackDocument(data?.idBackDocument);
      setIdFrontDocument(data?.idFrontDocument);
      setSelfieVerificationDocument(data?.selfieVerificationDocument);
      setSiaLicenceCardPhoto(data?.siaLicenceCardPhoto);
      setSiaLicenceExpiryDate(data?.siaLicenceExpiryDate);
      setSiaLicenceNumber(data?.siaLicenceNumber);
      setReportCount(data?.reportCount);

      if (data?.profilePhoto) {
        if (!data.profilePhoto?.includes('app.surelysecurity.com/storage/')) {
          setProfilePhoto(`https://app.surelysecurity.com/storage/${data?.profilePhoto}`);
        } else setProfilePhoto(data?.profilePhoto || '');
      }
      if (data?.profileVideo) {
        if (!data.profileVideo.includes('app.surelysecurity.com/storage/')) {
          setProfileVideo(`https://app.surelysecurity.com/storage/${data?.profileVideo}`);
        } else setProfileVideo(data.profileVideo || '');
      }
      if (data?.additionalPictures?.length !== 0) {
        const finalPictures: any = [];
        data?.additionalPictures?.forEach(
          (pic: string, index: number) =>
            additionalPictures.length < 4 &&
            pic &&
            finalPictures.push(!pic.includes('app.surelysecurity.com/storage/') ? `https://app.surelysecurity.com/storage/${pic}` : pic),
        );

        setAdditionalPictures(finalPictures || '');
      }
      settestlang(data?.testlang);
      setEmail(data?.email);
      setPostalCity(data?.postalCity);
      setAddressLine(data?.addressLine);
      setCounty(data?.county);
      setlocationRange(data?.locationRange);
      setPhoneNumber(data?.phoneNumber);
      setPostalCode(data?.postalCode);
      setInstantBook(data?.instantBook);
      setSiaLicence(data?.siaLicence);
      setIdCheck(data?.idCheck);
      setProofOfAddress(data?.proofOfAddress);
      setEmploymentHistory(data?.employmentHistory);
      setCreditCheck(data?.creditCheck);
      setNoCriminalRecord(data?.noCriminalRecord);
      setName(data?.name);
      setClientRepresentBusinessAs(data?.clientRepresentBusinessAs || null);
      setCompanyRegisteredNumber(data?.companyRegisteredNumber || null);
      setLatMap(data?.lat);
      setLngMap(data?.lng);
    });
  };

  const handleGetSiaLicence = () => {
    getSiaLicence().then((data: any) => {
      setSiaLicence(data?.data?.sia_licence);
      setIdCheck(data?.data?.id_check);
      setProofOfAddress(data?.data?.proof_of_address);
      setEmploymentHistory(data?.data?.employment_history);
      setCreditCheck(data?.data?.credit_check);
      setNoCriminalRecord(data?.data?.no_criminal_record);
    });
  };

  const handleGetProfileClientData = () => {
    getBankCardDetails().then((data: any) => {
      if (Array.isArray(data.data) && data.data.length > 0) {
        const lastItem = data.data[data.data.length - 1];
        const formattedCardNumber = '**** **** **** ' + lastItem.last_numbers;
        setHasCreditCard(!!formattedCardNumber);
      }
    });
    getProfileOperatorData().then((data: any) => {
      setIndustrySectors(data?.industrySectors || []);
      setEmail(data?.email);
      setPostalCity(data?.postalCity);
      setAddressLine(data?.addressLine);
      setCounty(data?.county);
      setlocationRange(data?.locationRange);
      setPhoneNumber(data?.phoneNumber);
      setPostalCode(data?.postalCode);
      setName(data?.name);
      setClientRepresentBusinessAs(data?.clientRepresentBusinessAs || null);
      setCompanyRegisteredNumber(data?.companyRegisteredNumber || null);
      setLatMap(data?.lat);
      setLngMap(data?.lng);
    });
  };

  const refreshBankAccountStatus = () => {
    getBankAccount().then((res) => {
      // Only set to true if we have valid bank account data
      // If there's an error or no data, hasBankAccount should be false
      setHasBankAccount(!res?.error && !!res?.data?.last_digits);
    }).catch((error) => {
      // Silently handle the case where user has no bank account
      // This is expected behavior, not an error that needs to be logged
      setHasBankAccount(false);
    });
  };

  const fetchAppData = () => {
    if (isClient) {
      handleGetProfileClientData();
      handleGetNotificationSettings();
    } else if (isClient === false) {
      handleGetProfileOperatorData();
      handleGetProfileDetails();
      handleGetSiaLicence();
      handleGetNotificationSettings();
      refreshBankAccountStatus();
      handleGetPositions();
      handleGetSurelyProBadges();
    }
  };

  useEffect(() => {
    if (isAuthenticated) fetchAppData();
    else return;
  }, [isAuthenticated]);

  const contextValue = {
    fetchAppData,
    profileTitle,
    profileDescription,
    setProfileTitle,
    setProfileDescription,
    positions,
    setPositions,
    qualifications,
    setQualifications,
    cityInfo,
    setCityInfo,
    email,
    setEmail,
    phoneNumber,
    setPhoneNumber,
    postalCity,
    setPostalCity,
    postalCode,
    setPostalCode,
    addressLine,
    setAddressLine,
    county,
    setCounty,
    locationRange,
    setlocationRange,
    profilePhoto,
    setProfilePhoto,
    profileVideo,
    setProfileVideo,
    siaLicense,
    setSiaLicense,
    additionalPictures,
    setAdditionalPictures,
    notificationSettings,
    instantBook,
    setInstantBook,
    siaLicence,
    setSiaLicence,
    idCheck,
    setIdCheck,
    proofOfAddress,
    setProofOfAddress,
    employmentHistory,
    setEmploymentHistory,
    creditCheck,
    setCreditCheck,
    noCriminalRecord,
    setNoCriminalRecord,
    industrySectors,
    setIndustrySectors,
    testlang,
    settestlang,
    addressVerificationDocument,
    setAddressVerificationDocument,
    documentType,
    setDocumentType,
    idBackDocument,
    setIdBackDocument,
    idFrontDocument,
    setIdFrontDocument,
    selfieVerificationDocument,
    setSelfieVerificationDocument,
    siaLicenceCardPhoto,
    setSiaLicenceCardPhoto,
    siaLicenceExpiryDate,
    setSiaLicenceExpiryDate,
    siaLicenceNumber,
    setSiaLicenceNumber,
    name,
    setName,
    completenessValue,
    setCompletenessValue,
    clientRepresentBusinessAs,
    setClientRepresentBusinessAs,
    companyRegisteredNumber,
    setCompanyRegisteredNumber,
    favouriteJobs,
    favouriteOperators,
    toggleFavouriteJob,
    toggleFavouriteOperator,
    surelyProBadges,
    otherReview,
    setOtherReview,
    overallReview,
    setOverallReview,
    operatorId,
    setOperatorId,
    reportCount,
    setReportCount,
    latMap,
    setLatMap,
    lngMap,
    setLngMap,
    jobDataOperator,
    setJobDataOperator,
    hasCreditCard,
    hasBankAccount,
    refreshBankAccountStatus,
  };

  return <AppContext.Provider value={contextValue}>{children}</AppContext.Provider>;
};
