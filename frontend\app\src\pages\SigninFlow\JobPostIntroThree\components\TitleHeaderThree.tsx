import React from 'react';
import { titleHeaderStyles } from '../../JobPostIntro/styles';

interface TitleHeaderThreeProps {
  text?: string | React.ReactNode;
}

const defaultText = (
  <>
    <div>Before we start,</div>
    <div>how do you want to do this?</div>
  </>
);

const TitleHeaderThree: React.FC<TitleHeaderThreeProps> = ({ text = defaultText }) => (
  <h1 style={{ 
    ...titleHeaderStyles, 
    display: 'flex',
    flexDirection: 'column',
    gap: '6px'
  }}>
    {text}
  </h1>
);

export default TitleHeaderThree;
