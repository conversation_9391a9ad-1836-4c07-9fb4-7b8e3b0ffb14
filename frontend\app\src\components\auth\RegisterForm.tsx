// @ts-nocheck
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import { useModalAction, useModalState } from 'src/context/ModalContext';
import { useRegistrationContext } from 'src/context/RegistrationContext';
import linkedin from '../../assets/icons/socialicon/linkedin.svg';
import google from '../../assets/icons/socialicon/google.png';
import { useState, useEffect } from 'react';
import { LinkedIn } from 'react-linkedin-login-oauth2';
import { Text, View, Image } from 'reshaped';
import { useToastSystem } from 'src/context/ToastSystemContext';
import surleyicon from '../../assets/icons/surleyicon/surleyicon.png';
import { checkTakenEmail } from '../../services/user';
import closeIcon from '../../assets/images/signup/close-icon.svg';

type FormDataType = {
  fullName: string;
  email: string;
  password: string;
  agreeToTerms: boolean;
};

const RegisterForm = ({ toOtpLogin, userType }: { toOtpLogin?: Function; userType: string | null }) => {
  const navigate = useNavigate();
  const { closeModal } = useModalAction();
  const { setSharedRegisterData } = useRegistrationContext();
  const location = useLocation();
  const toastSystem = useToastSystem();
  const nonClosing = localStorage?.getItem('wpRedirect') === 'true';

  const [referralToken, setReferralToken] = useState('');
  const [emailTaken, setEmailTaken] = useState(false);

  const schema = yup.object().shape({
    fullName: yup.string().max(64, 'Full name must not be longer than 64 characters.').required('Full name is required.'),
    email: yup.string().email('Please enter a valid email address.').required('Email is required.'),
    password: yup
      .string()
      .min(8, 'Password must contain at least 8 characters.')
      .max(32, 'Password must not be longer than 32 characters.')
      .required('Please enter your password')
      .matches(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\$%\^&\*])(?=.{8,})/,
        'Password must contain a minimum of 8 characters, including at least one uppercase letter, one lowercase letter, one number, and one special character.',
      ),
    agreeToTerms: yup.boolean().required('You must agree to the Terms & Conditions').oneOf([true], 'You must agree to the Terms & Conditions'),
  });
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  
  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  const navigateHandler = () => {
    if (localStorage.getItem('isLinkedInReady') === 'true') {
      closeModal();
      navigate('/crucial-data', { state: { userType: userType } });
    }
  };

  useEffect(() => {
    window.addEventListener('storage', navigateHandler);

    return () => {
      // Remove event listener
      window.removeEventListener('storage', navigateHandler);
    };
  }, []);

  useEffect(() => {
    const queryParams = new URLSearchParams(window.location.search);
    const action = queryParams.get('action');
    const referalToken = queryParams.get('referal_token');
    if (action === 'sign_up' && referalToken) {
      // openModal('REGISTER');
      setReferralToken(referalToken);
    }
  }, []);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<FormDataType>({
    resolver: yupResolver(schema),
  });
  const { openModal } = useModalAction();

  const onSubmitHandler = (data: any) => {
    // Split full name into first and last name
    const nameParts = data.fullName.trim().split(' ');
    const firstName = nameParts[0] || '';
    const lastName = nameParts.slice(1).join(' ') || '';
    
    setSharedRegisterData((sharedRegisterData: any) => ({
      ...sharedRegisterData,
      baseData: {
        ...sharedRegisterData.baseData,
        firstName,
        lastName,
        email: data.email,
        password: data.password,
        agreeToTerms: data.agreeToTerms,
        passwordConfirmation: data.password,
      },
    }));

    reset();
    closeModal(true);
    navigate('/job-post-intro-crucial', { state: { userType: userType, referralToken } });
  };

  const onSubmit = async (data: any) => {
    try {
      const response = await checkTakenEmail({
        email: data.email
      });

      if (response.error === false) {
        setEmailTaken(false);
        onSubmitHandler(data);
      } else if (response.error === true) {
        setEmailTaken(true);
        toastSystem.showError('Email already exists', 'Please use a different email address or log in.');
      }
    } catch (error) {
      console.error('Error checking email:', error);
      toastSystem.showError('Error', 'Something went wrong. Please try again.');
    }
  };

  const googleRegisterHandler = () => {
    try {
      const googleUrl = import.meta.env.VITE_REACT_APP_GOOGLE_URL_REGISTER as string;
      if (!googleUrl) {
        console.error('Google register URL not found in environment variables');
        toastSystem.showError('Error', 'Unable to connect to Google. Please try again later.');
        return;
      }
      window.location.href = googleUrl;
    } catch (error) {
      console.error('Error during Google registration:', error);
      toastSystem.showError('Error', 'Something went wrong with Google login. Please try again.');
    }
  };

  const onSuccessLinkedinHandler = (e: any) => {
    try {
      if (e && e.code) {
        // Store LinkedIn code in localStorage to be used in the backend
        localStorage.setItem('linkedInCode', e.code);
        localStorage.setItem('isLinkedInReady', 'true');
        
        // Trigger the storage event for the navigateHandler
        window.dispatchEvent(new Event('storage'));
      } else {
        console.error('LinkedIn login failed: No authorization code received');
        toastSystem.showError('LinkedIn Login Failed', 'Unable to authenticate with LinkedIn. Please try again.');
      }
    } catch (error) {
      console.error('Error during LinkedIn authentication:', error);
      toastSystem.showError('Error', 'Something went wrong with LinkedIn login. Please try again.');
    }
  };

  useEffect(() => {
    if (errors.fullName) {
      toastSystem.showError('Enter your full name.', 'This field cannot be left blank.');
    }

    if (errors.email) {
      toastSystem.showError('Enter your email.', 'Alternatively, you can enter your mobile number. This field cannot be left blank.');
    }
    if (errors.password) {
      toastSystem.showError('Enter your password.', 'Password must contain a minimum of 8 characters, including at least one uppercase letter, one lowercase letter, one number, and one special character.');
    }
    if (errors.agreeToTerms) {
      toastSystem.showError('You must agree to the Terms & Conditions.', '');
    }
  }, [errors, toastSystem]);

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)} className='flex flex-col items-center w-full'>
        <div className='flex w-full justify-between items-center mb-6'>
          <h1 className='font-rufina-stencil text-[32px] font-normal text-black'>Sign up</h1>
          <img 
            src={closeIcon} 
            alt="Close" 
            className='cursor-pointer w-5 h-5' 
            onClick={() => !nonClosing && closeModal()} 
          />
        </div>
        
        <div className='w-full mb-4'>
          <label className='block text-sm font-medium text-gray-900 mb-1'>Full name</label>
          <input
            name='fullName'
            type='text'
            className={`w-full h-12 px-3 py-2 border ${errors?.fullName?.message ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500`}
            {...register('fullName')}
          />
          {errors.fullName && <p className='mt-1 text-xs text-red-500'>{errors.fullName.message}</p>}
        </div>

        <div className='w-full mb-4'>
          <label className='block text-sm font-medium text-gray-900 mb-1'>Email address</label>
          <input
            name='email'
            type='email'
            className={`w-full h-12 px-3 py-2 border ${errors?.email?.message || emailTaken ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500`}
            {...register('email')}
          />
          {errors.email && <p className='mt-1 text-xs text-red-500'>{errors.email.message}</p>}
          {emailTaken && <p className='mt-1 text-xs text-red-500'>Email already exists</p>}
        </div>

        <div className='w-full mb-4'>
          <label className='block text-sm font-medium text-gray-900 mb-1'>Password</label>
          <div className='relative'>
            <input
              name='password'
              type={isPasswordVisible ? 'text' : 'password'}
              className={`w-full h-12 px-3 py-2 border ${errors?.password?.message ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500`}
              {...register('password')}
            />
            <span
              className='material-icons absolute right-4 top-3 cursor-pointer text-black-500'
              onClick={togglePasswordVisibility}
            >
              {isPasswordVisible ? 'visibility_off' : 'visibility'}
            </span>
          </div>
          {errors.password && <p className='mt-1 text-xs text-red-500'>{errors.password.message}</p>}
        </div>

        <div className='w-full mb-6 flex items-start'>
          <input 
            type='checkbox' 
            id='agreeToTerms'
            className={`mt-0.5 mr-2 h-4 w-4 ${errors?.agreeToTerms?.message ? 'border-red-500' : 'border-gray-300'}`}
            {...register('agreeToTerms')} 
          />
          <label htmlFor='agreeToTerms' className='text-xs text-gray-700'>
            I agree to the <a href='#' className='text-blue-500' onClick={() => window.open('https://surelysecurity.com/terms-of-use', '_blank', 'noopener,noreferrer')}>T&C</a> and <a href='#' className='text-blue-500' onClick={() => window.open('https://surelysecurity.com/code-of-conduct', '_blank', 'noopener,noreferrer')}>Code of Conduct</a>
          </label>
        </div>
        {errors.agreeToTerms && <p className='w-full -mt-4 mb-4 text-xs text-red-500'>{errors.agreeToTerms.message}</p>}

        <button
          type='submit'
          className='w-full h-12 bg-blue-500 text-white font-medium rounded-md hover:bg-blue-600 transition-colors'
        >
          Sign Up
        </button>
        <div className='my-4 flex w-full items-center'>
          <hr className='flex-grow border-t border-gray-300' />
          <span className='mx-2 text-xs text-gray-500'>or</span>
          <hr className='flex-grow border-t border-gray-300' />
        </div>
        
        <div className='w-full grid grid-cols-2 gap-4'>
          <button
            type='button'
            onClick={googleRegisterHandler}
            className='flex h-10 items-center justify-center gap-2 rounded-md bg-gray-100 hover:bg-gray-200 transition-colors'
          >
            <img src={google} alt='Google' className='h-5 w-5 mr-1' />
            <span className='text-sm font-medium'>Google</span>
          </button>
          
          <LinkedIn
            scope='openid email profile'
            clientId={import.meta.env.VITE_REACT_APP_LINKEDIN_CLIENT_ID}
            redirectUri={import.meta.env.VITE_REACT_APP_LINKEDIN_REGISTER_REDIRECT_URL}
            onSuccess={onSuccessLinkedinHandler}
            onError={(e) => console.error(e)}
          >
            {(renderProps) => (
              <button
                onClick={renderProps.linkedInLogin}
                className='flex h-10 items-center justify-center gap-2 rounded-md bg-gray-100 hover:bg-gray-200 transition-colors'
              >
                <img src={linkedin} alt='LinkedIn' className='h-5 w-5 mr-1' />
                <span className='text-sm font-medium'>LinkedIn</span>
              </button>
            )}
          </LinkedIn>
        </div>
        
        <div className='w-full mt-4 text-center'>
          <p className='text-xs text-gray-600'>
            Already have an account? <a href='#' className='text-blue-500' onClick={(e) => {
              e.preventDefault();
              closeModal();
              setTimeout(() => openModal('LOGIN'), 100);
            }}>Log In</a>
          </p>
        </div>
      </form>
    </>
  );
};

export default RegisterForm;
