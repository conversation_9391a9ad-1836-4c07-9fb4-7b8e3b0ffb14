// @ts-nocheck
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/bootstrap.css';
import axios from 'axios';
import { AuthContext } from 'src/context/AuthContext';
import { useContext } from 'react';
import { sendCode } from 'src/services/user';
import { useNavigate } from 'react-router-dom';
import { useModalAction } from 'src/context/ModalContext';
import { Image } from 'reshaped';
import { useToastSystem } from 'src/context/ToastSystemContext';
import surleyicon from '../../assets/icons/surleyicon/surleyicon.png';

type FormDataType = {
  email: string;
  password: string;
  login_type: number;
  platform: number;
  app_version: string;
  firebase_token: string;
  phone: number;
};

const OtpLoginForm = ({
  toStandardLogin,
  handleGoToRegister,
}: {
  toStandardLogin: Function;
  handleGoToRegister: Function;
}) => {
  const { authenticateUser } = useContext(AuthContext);
  const { openModal, closeModal } = useModalAction();
  const toastSystem = useToastSystem();
  const navigate = useNavigate()

  const schema: any = yup.object().shape({
    phone: yup.string().required('Please input your phone number.'),
  });

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
  } = useForm<FormDataType>({
    resolver: yupResolver<FormDataType>(schema),
    defaultValues: {
      email: '',
      password: '',
      login_type: 1,
      platform: 3,
      app_version: 'web 1.0',
      firebase_token: 'test',
    },
  });

  const onSubmitHandler = async (data: any) => {
    const result = await sendCode({phone: data.phone} )
    if(!result.error){
      navigate('/verify-opt-code', {
        state: {
          user_id: result.data.user_id
        }
      })
      closeModal(true)
      reset()
    }
    else{
      toastSystem.showError('Error', 'User not found!');
    }
  };

  return (
    <form
      onSubmit={handleSubmit(onSubmitHandler)}
      className='flex justify-center items-center'
    >
      <article className='max-w-lg pl-8 pr-8'>
        <section className='text-sm rubik text-black-white-black mb-3'>
          Access your existing account using your phone number. Don’t have an
          account?
          <a
            href='#'
            className='text-blue-500 ml-1'
            onClick={() => handleGoToRegister()}
          >
            Sign up now
          </a>
        </section>
        <section className='text-sm rubik text-black mb-3'>
          Your Phone Number
        </section>
        <Controller
        control={control}
        name='phone'
        render={({ field }) => (
          <PhoneInput
            country={'gb'}
            inputClass={`w-full grow-1 text-sm text-[#3C455D] rubik text-black flex-1 h-[48px] border rounded px-3 pl-2 ${
              errors?.phone?.message ? '!border-[#CB101D]' : '!border-black'
            }`}
            containerClass='flex'
            value={field.value} // Pass the field value to the PhoneInput
            onChange={(value) => field.onChange(value)} // Update the field value on change
          />
        )}
      />
        {/* <Controller control={control} name='phone' render={(props) => ( 
          <PhoneInput
            country={'gb'}
            inputClass={`w-full grow-1 text-sm text-[#3C455D] rubik text-black flex-1 h-[48px] border rounded px-3 pl-2 ${errors?.phone?.message ? '!border-[#CB101D]' : '!border-black'}`}
            containerClass='flex'
            {...props}
          />)}
        /> */}
        <p className='mt-1.5 font-medium text-[#CB101D]'>{errors.phone?.message}</p>
        {/* <div className='flex items-center mb-3'>
          <div className='relative'>
            <select
              required
              className='text-sm rubik text-black w-16 h-[48px] border border-black rounded pl-4 pr-2 appearance-none'
            >
              <option value='1'>+44</option>
              <option value='91'>+355</option>
            </select>
          </div>
          <input
            {...register('phone')}
            placeholder='Phone'
            type='tel'
            required
            className='text-sm rubik text-black flex-1 h-[48px] border border-black rounded pl-2'
          />
        </div> */}
        <section className='text-sm rubik text-black mb-3 mt-2'>
          We’ll verify your account through text message with a four-digit code.
        </section>
        <a
          href='#'
          className='text-black underline'
          onClick={() => toStandardLogin()}
        >
          Use email instead
        </a>
        <button className='flex justify-center items-center self-stretch px-4 py-2 gap-2 self-stretch border border-neutral rounded bg-background-base text-white bg-[#0B80E7] w-full sm:w-[376px] h-[48px] mt-4'>
          Log In
        </button>
      </article>
    </form>
  );
};

export default OtpLoginForm;
