// @ts-nocheck
import React, { useContext, useEffect, useState } from 'react';
import { Text, View, Image, Card, Breadcrumbs } from 'reshaped';
import { useNavigate, useParams } from 'react-router-dom';
import BadgeEmergencyHire from '../cards/JobCard/BadgeEmergencyHire/BadgeEmergencyHire';
import { JobContext } from 'src/context/JobContext';
import Loading from '../Loading/Loading';
import homebreadcrumbsicon from '../../assets/icons/homebreadcrumbsicon/homebreadcrumbsicon.svg';
import NoJobPhoto from '../NoData/NoJobPhoto';
import JobPhotoCarousel from './JobPhotoCarousel/JobPhotoCarousel';

const JobDescription: React.FC = ({ selectedJob }) => {
  const navigate = useNavigate();
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  const validImages = selectedJob?.images.filter((image: any) => image && image !== '');

  const getValidImageUrl = (images: string[]) => {
    if (!images || images?.length === 0) {
      return '';
    }
    if (images.length === 1) {
      return images[0].startsWith('https://app.surelysecurity.com/storage/') ? images[0] : 'https://app.surelysecurity.com/storage/' + images[0];
    }
    if (images.length > 1) {
      const formattedImages = images.map((image: string) =>
        image.startsWith('https://app.surelysecurity.com/storage/') ? image : 'https://app.surelysecurity.com/storage/' + image,
      );
      return formattedImages;
    }
  };

  const imagesWithUrl = getValidImageUrl(validImages);

  const handleImageClick = (index: any) => {
    setSelectedImageIndex(index);
  };

  const allImagesEmpty = !validImages || validImages?.length === 0;

  return (
    <View width={'100%'} gap={7}>
      <Breadcrumbs className='mb-[20px]'>
        <Breadcrumbs.Item onClick={() => navigate('/search-jobs')}>
          <Text className='rubik text-[14px] font-normal leading-[20px] text-[#3C455D]'>Search page</Text>
        </Breadcrumbs.Item>
        <Breadcrumbs.Item onClick={() => {}}>
          <Text className='rubik text-[14px] font-normal leading-[20px] text-[#3C455D]'>{selectedJob?.post_name}</Text>
        </Breadcrumbs.Item>
      </Breadcrumbs>
      <View direction={'row'} justify={'start'} align={'center'} gap={3}>
        <Text className='font-rufina-stencil text-[32px] font-normal leading-[40px]'>{selectedJob?.post_name}</Text>
        {selectedJob?.is_emergency_hire ? <BadgeEmergencyHire /> : null}
      </View>

      <JobPhotoCarousel
        images={imagesWithUrl}
        allImagesEmpty={allImagesEmpty}
        validImages={validImages}
        handleImageClick={handleImageClick}
        selectedImageIndex={selectedImageIndex}
      />

      <View width={'100%'}>
        <Card className='mb-[20px] sm:mb-[68px]'>
          <View align={'start'}>
            <Text className='rubik mb-5 text-[16px] font-medium leading-[20px] text-[#1A1A1A]'>Description</Text>
            <Text align={'start'} className='rubik text-[14px] font-normal leading-[20px] text-[#383838]'>
              {selectedJob?.description || 'No data for description'}
            </Text>
          </View>

          <View align={'start'}>
            <Text className='rubik my-5 text-[16px] font-medium leading-[20px] text-[#1A1A1A]'>Notes or Special Requirements</Text>
            <Text align={'start'} className='rubik text-[14px] font-normal leading-[20px] text-[#383838]'>
              {selectedJob?.duty_of_care || 'No data for duty of care'}
            </Text>
          </View>

        </Card>
      </View>
    </View>
  );
};

export default JobDescription;
