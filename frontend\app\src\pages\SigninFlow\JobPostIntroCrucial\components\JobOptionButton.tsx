import React from 'react';
import paperIcon from '../../../../assets/images/job-post-intro/paper.png';

interface JobOptionButtonProps {
  isSelected: boolean;
  onClick: () => void;
}

const JobOptionButton: React.FC<JobOptionButtonProps> = ({ isSelected, onClick }) => {
  return (
    <button
      onClick={onClick}
      style={{
        width: '364px',
        height: '140px',
        backgroundColor: 'rgb(250, 250, 250)',
        border: isSelected ? '4px solid #3B82F6' : '1px solid #E5E7EB',
        borderRadius: '12px',
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'flex-start',
        flexDirection: 'row',
        padding: '0 40px',
        gap: '40px',
        outline: 'none'
      }}
    >
      <img 
        src={paperIcon} 
        alt="Looking for a job" 
        style={{
          width: '54px',
          height: '65px'
        }}
      />
      <span style={{
        fontFamily: 'Rubik',
        fontSize: '20px',
        fontWeight: 700,
        color: 'rgb(50, 61, 88)',
        lineHeight: '24px',
        textAlign: 'left'
      }}>
        Looking for a job
      </span>
    </button>
  );
};

export default JobOptionButton;
