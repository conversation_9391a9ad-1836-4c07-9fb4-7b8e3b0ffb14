// @ts-nocheck
import React, { useState, useEffect, useContext } from 'react';
import { Card, View, Divider, Image, Text, Button, useToggle, Icon, Tabs, Loader, Badge } from 'reshaped';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { createChat } from 'src/services/chat';

import AppliedOperatorProfileModal from './AppliedOperatorProfileModal/AppliedOperatorProfileModal';
import inclusivitypledgeicon from '../../assets/icons/inclusivitypledgeicon/inclusivitypledgeicon.svg';
import { IdVerified } from 'src/assets/icons';
import Job from 'src/services/jobs';
import { AnyAction } from '@reduxjs/toolkit';
import moment from 'moment';
import NoOperatorApplied from './NoOperatorApplied/NoOperatorApplied';
import { JobContext } from 'src/context/JobContext';
import ConfirmationModal from 'src/components/UI/ConfirmationModal/ConfirmationModal';
import NoJobClient from 'src/components/NoData/NoJobClient';
import Loading from 'src/components/Loading/Loading';
import '../../index.css';

const ApplicationStatus: React.FC<{ status: string; name: any; id: any; operatorId: any }> = ({ status, name, id, operatorId }) => {
  const navigate = useNavigate();
  let backgroundColor;
  switch (status) {
    case 'invited':
      backgroundColor = '!bg-[#BABABC]';
      break;
    case 'accepted':
      backgroundColor = '!bg-[#0B80E7]';
      break;
    case 'cancelled':
      backgroundColor = '!bg-[#CE0E0E]';
      break;
    case 'declined':
      backgroundColor = '!bg-[#CE0E0E]';
      break;
    default:
      backgroundColor = '!bg-[#BABABC]';
      break;
  }
  return (
    <View className='flex flex-row justify-between gap-3'>
      {status === 'accepted' && (
        <Button
          variant='outline'
          className='flex h-[48px] items-center justify-center self-stretch rounded-[8px] border border-[#DFE2EA] sm:w-[124px]'
          onClick={() => {
            navigate(`/chat`, { state: { operator_id: operatorId } });
          }}
        >
          <Text className='rubik px-4 text-[16px] font-medium capitalize text-[#383838]'>Chat</Text>
        </Button>
      )}
      <View className={`flex h-[48px] items-center justify-center self-stretch rounded-[8px] ${backgroundColor} sm:w-[124px]`}>
        <Text className='rubik px-4 text-[16px] font-medium capitalize text-[#FFFF]'>{status}</Text>
      </View>
    </View>
  );
};

const ManageJobsPage: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { allJobs, fetchAllJobs, isLoadingJobs, setIsLoadingApplications, isLoadingApplications } = useContext(JobContext);
  const { active, activate, deactivate } = useToggle(false);

  const [showModal, setShowModal] = useState(false);
  const [modalJobId, setModalJobId] = useState<any>('');
  const [modalApplicantId, setModalApplicantId] = useState('');
  const [selectedOperator, setSelectedOperator] = useState<any>();
  const [applicantsData, setApplicantsData] = useState<any>();

  const redirectedToActivePage = location?.state?.activeTab || '1';
  const selectedJob = allJobs?.find((job: any) => job.id == id) || allJobs?.[0];

  const [activeTab, setActiveTab] = useState(redirectedToActivePage || '1');

  const handleModal = (operator: any) => {
    setSelectedOperator(operator);
    activate();
  };

  const acceptApplication = (applicantId: any) => {
    Job.acceptApplicant(id, applicantId).then(() => {
      Job.applicationsOf(id).then((data: any) => {
        setApplicantsData(data);
      });
    });
  };
  const handleActiveTab = (tab: any) => tab !== activeTab && setActiveTab(tab);

  const declineApplication = (applicantId: any) => {
    setShowModal(true);
    setModalJobId(id);
    setModalApplicantId(applicantId);
  };

  const declineApplicationApproved = () => {
    Job.declineApplicant(modalJobId, modalApplicantId).then(() => {
      Job.applicationsOf(modalJobId).then((data: any) => {
        setApplicantsData(data);
        setShowModal(false);
      });
    });
  };

  useEffect(() => {
    fetchAllJobs();
  }, []);

  useEffect(() => {
    if (allJobs && allJobs?.length > 0 && !id) {
      selectJob(allJobs?.[0]);
    } else {
      setIsLoadingApplications(true);
      Job.applicationsOf(id).then((data: any) => {
        setApplicantsData(data);
        setIsLoadingApplications(false);
      });
    }
  }, [id, allJobs]);

  const selectJob = (job: AnyAction) => {
    navigate(`/manage-jobs/${job?.id}`, { state: { job } });
  };

  const isAppliedFirst = applicantsData?.sort((a, b) => {
    if (a?.applicant?.is_invited === 1 && b?.applicant?.is_invited !== 1) {
      return -1; // a should come before b
    } else if (a?.applicant?.is_invited !== 1 && b?.applicant?.is_invited === 1) {
      return 1; // b should come before a
    } else {
      return 0; // leave them unchanged relative to each other
    }
  });

  const baseURL = 'https://app.surelysecurity.com/storage/';

  return (
    <div className='w-full'>
      <View className='hidden w-full flex-col items-start gap-4 px-4 sm:flex-row sm:gap-6 md:gap-8 md:px-8 lg:flex lg:gap-10 xl:gap-16 xl:px-0'>
        <div className='w-full rounded-lg border border-[#DFE2EA] bg-[#fff] sm:w-[264px]' style={{ flex: '0 0 264px' }}>
          <View className='flex w-full flex-row items-center justify-between p-[24px]'>
            <Text className='rubik text-[16px] font-medium text-[#1A1A1A]'>Your jobs</Text>
            <Button
              onClick={() => navigate('/post-job')}
              icon={() => <span className='material-icons-outlined mt-[-4px] text-[#0B80E7]'>add</span>}
              className='btn-no-hover !bg-transparent'
            >
              <Text className='rubik text-base font-medium leading-6'>Post a job</Text>
            </Button>
          </View>
          <div className='h-[1px] w-full !bg-[#DFE2EA] ' />
          <View className='flex h-[548px] flex-col gap-[16px] overflow-auto p-[24px] '>
            {isLoadingJobs ? (
              <Loader size='medium' className='mx-auto mt-10 h-[100px] w-[100px]' />
            ) : (
              allJobs?.map((job: any) => (
                <div
                  key={'job-' + job?.id}
                  className={`flex flex-col items-start justify-center gap-[20px] self-stretch rounded-[8px] border p-[16px]  ${
                    selectedJob?.id === job?.id ? 'bg-[#BABABC]' : 'bg-[#F4F5F7]'
                  } ${selectedJob?.id === job?.id ? 'border-[#0B80E7]' : 'bg-[#DBDFEA]'}`}
                  onClick={() => selectJob(job)}
                >
                  <View className='flex flex-col items-start gap-[8px] '>
                    <Text className='rubik text-[13px] font-normal leading-[16px] text-[#444B5F] '>
                      {moment(job.updated_at).format('ddd D MMM YYYY')}
                    </Text>
                    <Text className='rubik max-w-[172px] overflow-hidden truncate whitespace-normal text-left text-[16px] font-medium leading-[20px] text-[#000]'>
                      {job.post_name}
                    </Text>
                  </View>
                  <View className='flex w-full flex-row items-center justify-between'>
                    <button
                      className='rubik btn-no-hover !py-0 text-[13px] font-normal leading-[16px] !text-[#0B80E7] '
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        navigate(`/edit-job/${job.id}`, { state: { job } });
                      }}
                    >
                      Edit
                    </button>
                    <Text className='rubik text-[13px] font-medium leading-[16px] text-[#323C58] '>{job?.applicants?.length || 0} proposals</Text>
                  </View>
                </div>
              ))
            )}
            {!isLoadingJobs && allJobs?.length === 0 && <NoJobClient />}
          </View>
        </div>
        {selectedJob && (
          <View className='mt-[30px] sm:mt-0'>
            <View className='flex flex-col items-start pr-8 '>
              <Text className='rubik mb-4 text-[20px] font-normal leading-[28px] text-[#1A1A1A] lg:mb-0'>{selectedJob?.post_name}:</Text>
              <div
                className={`snap-mandatory ${isLoadingApplications ? 'overflow-hidden' : 'overflow-auto'} w-[650px] xl:w-[900px] 2xl:w-[1000px] min-[1800px]:w-[1200px]`}
              >
                <div
                  className={`flex h-max w-full gap-6 pb-6 lg:pb-14 ${isLoadingApplications || applicantsData?.length === 0 ? 'pr-0' : 'pr-[500px]'}`}
                >
                  {isLoadingApplications ? (
                    <Loader size='medium' className='mx-auto mt-40 h-[100px] w-[100px]' />
                  ) : (
                    isAppliedFirst?.map((application: any) => {
                      const isInvitedBadge = !!application?.applicant?.is_invited;
                      return (
                        <div
                          key={'application-' + application.applicant?.user?.id}
                          className='mt-4 min-h-[540px] w-[312px] grow snap-start px-0.5 pt-0 lg:mt-0 lg:pt-8'
                          style={{ flex: '0 0 314px' }}
                        >
                          <Badge.Container className={`relative h-full w-full ${isInvitedBadge ? 'invited-highlight' : ''}`}>
                            {isInvitedBadge && (
                              <Badge className='z-20 -mt-[9px] mr-10 !rounded-lg bg-[#0B80E7] !px-3 !py-1 font-normal text-white' size='small'>
                                INVITED
                              </Badge>
                            )}
                            <Card className='z-30 h-full p-[24px]'>
                              {/* <View className='flex h-full flex-col justify-between gap-[20px] '> */}
                              <View className='flex h-full flex-col'>
                                <div>
                                  <View className='flex items-center justify-between'>
                                    <div className='flex items-center'>
                                      {application?.applicant?.user?.sia_certificates?.[0]?.sia_licence === 1 &&
                                      application?.applicant?.user?.sia_certificates?.[0]?.id_check === 1 &&
                                      application?.applicant?.user?.sia_certificates?.[0]?.proof_of_address === 1 ? (
                                        <span className='rubik mr-[5px] h-[auto] w-[auto] rounded-md !bg-[#E6FEF3] p-1 text-xs font-normal leading-4 text-[#05751F]'>
                                          SIA CERTIFIED
                                        </span>
                                      ) : (
                                        <span className='rubik mr-[5px] h-[auto] w-[auto] rounded-md !bg-[#EDEDED] p-1 text-xs font-normal leading-4 text-[#6A6A6A]'>
                                          SIA PENDING
                                        </span>
                                      )}
                                      {application?.applicant?.instant_book ? (
                                        <span className='rubik mr-[5px] w-[auto] rounded-md !bg-[#D1E1FF] px-[8px] py-1 text-[12px] font-normal leading-4 text-[#0D2F87]'>
                                          INSTANT BOOK
                                        </span>
                                      ) : (
                                        <div />
                                      )}
                                    </div>
                                    <Button
                                      variant='ghost'
                                      icon={() => (
                                        <span
                                          className={`material-icons-outlined -mt-1  !text-[20px] text-base text-[#C7CDDB] ${
                                            application?.applicant?.is_favorite ? 'text-red-500' : 'text-[#C7CDDB]-500'
                                          }`}
                                        >
                                          favorite
                                        </span>
                                      )}
                                    />
                                  </View>
                                  <div
                                    className='mt-[7px] flex cursor-pointer flex-row items-center gap-4'
                                    onClick={() => navigate(`/operator-profile/${application?.applicant?.user?.id}`)}
                                  >
                                    <View>
                                      {application?.applicant?.user?.profile_photo ? (
                                        <div
                                          className='rounded-full p-0.5'
                                          style={{
                                            background: 'linear-gradient(180deg, rgba(50, 60, 88, 0.855), rgba(113, 225, 248, 0.9025))',
                                          }}
                                        >
                                          <Image
                                            className='flex h-[80px] w-[80px] flex-col items-start rounded-full bg-white'
                                            alt='Profile'
                                            src={
                                              application?.applicant?.user?.profile_photo.startsWith(baseURL)
                                                ? application?.applicant?.user?.profile_photo
                                                : baseURL + application?.applicant?.user?.profile_photo
                                            }
                                          />
                                        </div>
                                      ) : (
                                        <View className='flex h-[80px]  w-[80px] items-center justify-center  rounded-full bg-[#0B80E7]'>
                                          <Text className='rubik text-[30px] text-white'>{application?.applicant?.user?.name?.charAt(0)}</Text>
                                        </View>
                                      )}
                                      <Icon className='absolute bottom-0 right-0 h-[31px] w-[30px]' svg={IdVerified} />
                                    </View>
                                    <div className='flex flex-col justify-start gap-1'>
                                      {application?.applicant?.user?.overall_ratings?.count > 0 && (
                                        <div className='flex flex-row gap-2'>
                                          <span className='material-icons text-[20px] text-[#F4BF00]'>star</span>
                                          <Text className='rubik text-[15px] font-medium leading-[20px] text-[#323c58]'>
                                            {application.applicant.user.overall_ratings.value} (
                                            {application.applicant.user.overall_ratings.count})
                                          </Text>
                                        </div>
                                      )}
                                      <div>
                                        <Text className='rubik font-medium text-[#1A1A1A]'>{application?.applicant?.user?.name}</Text>
                                      </div>
                                      <div className='flex items-center'>
                                        {application?.applicant?.user?.address_2 ? (
                                          <>
                                            <Text className='rubik text-line-height text-[16px] leading-5   text-blue-400 '>
                                              {application?.applicant?.user?.address_2?.split(', ', 1).pop()}
                                            </Text>
                                            <span className='material-icons-outlined icon-line-height text-[15px] text-blue-400'>place</span>
                                          </>
                                        ) : (
                                          <></>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                  {/* <View className='flex flex-col items-start'>
                                    <Text className='rubik mr-[0px] mt-[20px] font-medium text-[#1A1A1A]'>
                                      Jobs completed: {application?.applicant?.user?.jobs?.completed_jobs}
                                    </Text>
                                  </View> */}
                                  <View className='mt-[20px] flex flex-wrap gap-2 '>
                                    {application?.applicant?.user?.sia_licence_types?.map((grade: any, index: any) => (
                                      <Button
                                        size='small'
                                        key={'license-' + index}
                                        rounded={true}
                                        elevated={false}
                                        className='max-w-xs overflow-hidden truncate border border-[#05751F] !bg-[#E6FEF3] px-2 py-1 text-xs text-[#323c58]'
                                      >
                                        <Text color='positive' className='rubik flex items-center gap-1'>
                                          <span className='material-icons text-[16px]'>star</span>
                                          {grade}
                                        </Text>
                                      </Button>
                                    ))}
                                    {application?.applicant?.user?.sia_certificates?.[0]?.sia_licence == 1 &&
                                      application?.applicant?.user?.sia_certificates?.[0]?.id_check == 1 &&
                                      application?.applicant?.user?.sia_certificates?.[0]?.employment_history == 1 &&
                                      application?.applicant?.user?.sia_certificates?.[0]?.no_criminal_record == 1 &&
                                      application?.applicant?.user?.sia_certificates?.[0]?.credit_check == 1 &&
                                      application?.applicant?.user?.sia_certificates?.[0]?.proof_of_address == 1 && (
                                        <View className='gap-2'>
                                          <Button
                                            size='small'
                                            rounded={true}
                                            elevated={false}
                                            className='mr-[5px] max-w-xs overflow-hidden  truncate border !bg-[#7CDBEF] px-2 py-1 text-xs text-[#323c58] '
                                          >
                                            <Text className='rubik flex items-center gap-1 text-[12px] font-medium text-[#323c58]'>
                                              <span className='material-icons text-[16px] text-[#323c58]'>security</span>
                                              BS7858
                                            </Text>
                                          </Button>
                                        </View>
                                      )}
                                    {application?.applicant?.user?.surely_pro_badge?.some((badge: any) => badge.type === 'InclusivityPledge') && (
                                      <Button
                                        size='small'
                                        variant='outline'
                                        className='border-dark-gradient flex w-[170px] items-center justify-center gap-1 self-stretch rounded-full border !bg-[#ffff] '
                                        icon={() => <img alt={''} src={inclusivitypledgeicon} className='w-[20px]' />}
                                      >
                                        <Text className='rubik text-[12px] font-medium not-italic leading-5 '>Inclusivity Pledge</Text>
                                      </Button>
                                    )}
                                  </View>
                                  <Divider className='mt-[20px] h-[1px] w-full' />
                                  <Text className='mt-[10px] break-all text-[14px] font-normal leading-[20px] text-[#383838]'>
                                    {application?.notes?.length > 150
                                      ? `${application?.notes.substring(0, 150)}...`
                                      : application?.notes ?? 'No notes provided'}
                                  </Text>
                                </div>
                                <div className='mt-auto'>
                                  <button
                                    onClick={() => handleModal(application.applicant)}
                                    className='btn-no-hover mt-[8px] flex h-[32px] w-[134px] !text-[#0B80E7] '
                                  >
                                    <Text className=' flex-start rubik text-[16px] font-medium leading-[24px]'>Read more</Text>
                                  </button>

                                  <Divider className='mt-[10px] h-[1px] w-full' />
                                  {application.status == 'pending' ? (
                                    <div className='mt-[15px] flex flex-row gap-[16px]'>
                                      <Button
                                        variant='outline'
                                        icon={() => <span className='material-icons -mt-1 text-[#CE0E0E]'>clear</span>}
                                        onClick={() => {
                                          declineApplication(application?.applicant?.user?.id);
                                        }}
                                        className='border-neutral flex h-[48px] items-center justify-center gap-2 rounded-[8px] border !border-[#DFE2EA] !bg-[#fff] px-4 py-2 sm:w-[124px]'
                                      >
                                        <Text className='rubik text-[16px] font-medium text-[#CE0E0E]'>Decline</Text>
                                      </Button>
                                      <Button
                                        onClick={() => {
                                          acceptApplication(application?.applicant?.user?.id);
                                        }}
                                        variant='outline'
                                        className='flex h-[48px] items-center justify-center self-stretch rounded-[8px]  !bg-[#0B80E7] sm:w-[124px]'
                                      >
                                        <Text className='rubik text-[16px] font-medium text-[#FFFF]'>Accept</Text>
                                      </Button>
                                    </div>
                                  ) : (
                                    <div className='mx-auto mt-[15px] flex justify-center'>
                                      <ApplicationStatus
                                        status={application?.status}
                                        id={application?.applicant?.user?.id}
                                        name={application?.applicant?.user?.name}
                                        operatorId={application?.applicant?.user?.id}
                                      />
                                    </div>
                                  )}
                                </div>
                              </View>
                            </Card>
                          </Badge.Container>
                        </div>
                      );
                    })
                  )}
                  {!isLoadingApplications && applicantsData?.length === 0 && (
                    <div className='mx-auto lg:mt-[10%]'>
                      <NoOperatorApplied />
                    </div>
                  )}
                </div>
              </div>
            </View>
          </View>
        )}

        {showModal && (
          <ConfirmationModal
            active={showModal}
            deactivate={() => setShowModal(false)}
            successAction={declineApplicationApproved}
            successLabel='Decline'
            title='Decline Applicant'
            description='Are you sure to decline this applicant'
          />
        )}
        {selectedOperator && <AppliedOperatorProfileModal active={active} deactivate={deactivate} selectedOperator={selectedOperator} />}
      </View>

      <div className='flex h-full flex-col gap-6 lg:hidden'>
        <Tabs value={activeTab} variant='borderless' itemWidth='equal'>
          <div className='sticky top-2 z-50 overflow-auto bg-[#FAFBFF] px-4 lg:px-0'>
            <Tabs.List>
              <Tabs.Item value='1'>
                <div onClick={() => handleActiveTab('1')}>Your Jobs</div>
              </Tabs.Item>
              <Tabs.Item value='2'>
                <div onClick={() => handleActiveTab('2')}>Applicants</div>
              </Tabs.Item>
            </Tabs.List>
          </div>
          <View.Item>
            <Tabs.Panel value='1'>
              <View className='px-4'>
                <div className='w-full rounded-lg border border-[#DFE2EA] bg-[#fff] ' style={{ flex: '0 0 264px' }}>
                  <View className='flex w-full flex-row items-center justify-between p-[24px]'>
                    <Text className='rubik text-[16px] font-medium text-[#1A1A1A]'>Your jobs</Text>
                    <Button
                      onClick={() => navigate('/post-job')}
                      icon={() => <span className='material-icons-outlined mt-[-4px] text-[#0B80E7]'>add</span>}
                      className='btn-no-hover !bg-transparent'
                    >
                      <Text className='rubik text-base font-medium leading-6'>Post a Job</Text>
                    </Button>
                  </View>
                  <div className='h-[1px] w-full !bg-[#DFE2EA] ' />

                  <View className='flex h-full max-h-[521px] flex-col gap-[16px] overflow-auto overflow-x-hidden p-[24px] '>
                    {isLoadingJobs ? (
                      <Loader size='medium' className='mx-auto mt-10 h-[100px] w-[100px]' />
                    ) : (
                      allJobs?.map((job: any) => (
                        <div
                          key={'job-' + job?.id}
                          className={`flex flex-col items-start justify-center gap-[20px] self-stretch rounded-[8px] border p-[16px] ${
                            selectedJob?.id === job?.id ? 'bg-[#BABABC]' : 'bg-[#F4F5F7]'
                          } ${selectedJob?.id === job?.id ? 'border-[#0B80E7]' : 'bg-[#DBDFEA]'}`}
                          onClick={() => {
                            selectJob(job);
                            handleActiveTab('2');
                          }}
                        >
                          <View className='flex flex-col items-start gap-[8px] '>
                            <Text className='rubik text-[13px] font-normal leading-[16px] text-[#444B5F] '>
                              {moment(job.updated_at).format('ddd D MMM YYYY')}
                            </Text>
                            <Text className='rubik text-left text-[16px] font-medium leading-[20px] text-[#000]'>{job?.post_name}xxxxx</Text>
                          </View>
                          <View className='flex w-full flex-row items-center justify-between'>
                            <button
                              className='rubik btn-no-hover !py-0 text-[13px] font-normal leading-[16px] !text-[#0B80E7] '
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                navigate(`/edit-job/${job.id}`, { state: { job } });
                              }}
                            >
                              Edit
                            </button>
                            <Text className='rubik text-[13px] font-medium leading-[16px] text-[#323C58] '>
                              {job?.applicants?.length || 0} proposals
                            </Text>
                          </View>
                        </div>
                      ))
                    )}
                    {!isLoadingJobs && allJobs?.length === 0 && <NoJobClient />}
                  </View>
                </div>
              </View>
            </Tabs.Panel>
            <Tabs.Panel value='2'>
              {selectedJob && (
                <View>
                  <View className='relative flex flex-col items-start px-4 '>
                    <Text className='rubik mb-4 text-[20px] font-normal leading-[28px] text-[#1A1A1A]'>{selectedJob?.post_name}:</Text>
                    {isLoadingApplications ? (
                      <Loading />
                    ) : (
                      <div className='w-full snap-mandatory overflow-visible lg:w-[1200px]'>
                        <div className='flex h-[calc(100dvh-263px)] flex-col gap-2 overflow-auto pb-6 pr-2'>
                          {applicantsData?.map((application: any, index: number) => {
                            const isInvitedBadge = !!application?.applicant?.is_invited;

                            return (
                              <div
                                key={'application-' + application.applicant.id}
                                className='mt-4 h-full  min-h-[540px] w-full snap-start'
                                style={{ flex: '0 0 320px' }}
                              >
                                <Badge.Container className='h-full w-full'>
                                  {isInvitedBadge && (
                                    <Badge className='mb-5 mr-10 !rounded-lg bg-[#0B80E7] !px-3 !py-1 font-normal text-white' size='small'>
                                      INVITED
                                    </Badge>
                                  )}
                                  <Card className={`h-full p-[24px] ${isInvitedBadge ? 'invited-highlight' : ''}`}>
                                    <View className='flex h-full flex-col justify-between gap-[20px] '>
                                      <div>
                                        <View className='flex items-center justify-between'>
                                          <div className='flex items-center'>
                                            {application?.applicant?.user?.sia_certificates?.[0]?.sia_licence === 1 &&
                                            application?.applicant?.user?.sia_certificates?.[0]?.id_check === 1 &&
                                            application?.applicant?.user?.sia_certificates?.[0]?.proof_of_address === 1 ? (
                                              <span className='rubik mr-[5px] h-[auto] w-[auto] rounded-md !bg-[#E6FEF3] p-1 text-xs font-normal leading-4 text-[#05751F]'>
                                                SIA CERTIFIED
                                              </span>
                                            ) : (
                                              <span className='rubik mr-[5px] h-[auto] w-[auto] rounded-md !bg-[#EDEDED] p-1 text-xs font-normal leading-4 text-[#6A6A6A]'>
                                                SIA PENDING
                                              </span>
                                            )}
                                            {application?.applicant?.instant_book ? (
                                              <span className='rubik mr-[5px] w-[auto] rounded-md !bg-[#D1E1FF] px-[8px] py-1 text-[12px] font-normal leading-4 text-[#0D2F87]'>
                                                INSTANT BOOK
                                              </span>
                                            ) : (
                                              <div />
                                            )}
                                          </div>
                                          <Button
                                            variant='ghost'
                                            icon={() => (
                                              <span
                                                className={`material-icons-outlined -mt-1  !text-[20px] text-base text-[#C7CDDB] ${
                                                  application?.applicant?.is_favorite ? 'text-red-500' : 'text-[#C7CDDB]-500'
                                                }`}
                                              >
                                                favorite
                                              </span>
                                            )}
                                          />
                                        </View>
                                        <div
                                          className='mt-[7px] flex cursor-pointer flex-row items-center gap-4'
                                          onClick={() => navigate(`/operator-profile/${application?.applicant?.user?.id}`)}
                                        >
                                          <View>
                                            {application?.applicant?.user?.profile_photo ? (
                                              <div
                                                className='rounded-full p-0.5'
                                                style={{
                                                  background: 'linear-gradient(180deg, rgba(50, 60, 88, 0.855), rgba(113, 225, 248, 0.9025))',
                                                }}
                                              >
                                                <Image
                                                  className='flex h-[80px] w-[80px] flex-col items-start rounded-full bg-white'
                                                  alt='Profile'
                                                  src={
                                                    application?.applicant?.user?.profile_photo.startsWith(baseURL)
                                                      ? application?.applicant?.user?.profile_photo
                                                      : baseURL + application?.applicant?.user?.profile_photo
                                                  }
                                                />
                                              </div>
                                            ) : (
                                              <View className='flex h-[80px]  w-[80px] items-center justify-center  rounded-full bg-[#0B80E7]'>
                                                <Text className='rubik text-[30px] text-white'>{application?.applicant?.user?.name?.charAt(0)}</Text>
                                              </View>
                                            )}
                                            <Icon className='absolute bottom-0 right-0 h-[31px] w-[30px]' svg={IdVerified} />
                                          </View>
                                          <div className='flex flex-col justify-start gap-1'>
                                            {application?.applicant?.user?.overall_ratings ? (
                                              <div className='flex flex-row gap-2'>
                                                <span className='material-icons text-[20px] text-[#F4BF00]'>star</span>
                                                <Text className='rubik text-[15px] font-medium leading-[20px] text-[#323c58]'>
                                                  {application?.applicant?.user?.overall_ratings?.value} (
                                                  {application?.applicant?.user?.overall_ratings?.count})
                                                </Text>
                                              </div>
                                            ) : (
                                              <div className='flex flex-row gap-2'>
                                                <span className='material-icons text-[20px] text-[#C7CDDB]'>star</span>
                                                <Text className='rubik text-[15px] font-medium leading-[20px] text-[#C7CDDB]'>No rating</Text>
                                              </div>
                                            )}
                                            <div>
                                              <Text className='rubik font-medium text-[#1A1A1A]'>{application?.applicant?.user?.name}</Text>
                                            </div>
                                            <div className='flex items-center'>
                                              {application?.applicant?.user?.address_2 ? (
                                                <>
                                                  <Text className='rubik text-line-height text-[16px] leading-5   text-blue-400 '>
                                                    {application?.applicant?.user?.address_2?.split(', ', 1).pop()}
                                                  </Text>
                                                  <span className='material-icons-outlined icon-line-height text-[15px] text-blue-400'>place</span>
                                                </>
                                              ) : (
                                                <></>
                                              )}
                                            </div>
                                          </div>
                                        </div>
                                        {/* <View className='flex flex-col items-start'>
                                          <Text className='rubik mr-[0px] mt-[20px] font-medium text-[#1A1A1A]'>
                                            Jobs completed: {application?.applicant?.user?.jobs?.completed_jobs}
                                          </Text>
                                        </View> */}
                                        <View className='mt-[20px] flex flex-wrap gap-2 '>
                                          {application?.applicant?.user?.sia_licence_types?.map((grade: any, index: any) => (
                                            <Button
                                              size='small'
                                              key={'license-' + index}
                                              rounded={true}
                                              elevated={false}
                                              className='max-w-xs overflow-hidden truncate border border-[#05751F] !bg-[#E6FEF3] px-2 py-1 text-xs text-[#323c58]'
                                            >
                                              <Text color='positive' className='rubik flex items-center gap-1'>
                                                <span className='material-icons text-[16px]'>star</span>
                                                {grade}
                                              </Text>
                                            </Button>
                                          ))}
                                          {application?.applicant?.user?.sia_certificates?.[0]?.sia_licence == 1 &&
                                            application?.applicant?.user?.sia_certificates?.[0]?.id_check == 1 &&
                                            application?.applicant?.user?.sia_certificates?.[0]?.employment_history == 1 &&
                                            application?.applicant?.user?.sia_certificates?.[0]?.no_criminal_record == 1 &&
                                            application?.applicant?.user?.sia_certificates?.[0]?.credit_check == 1 &&
                                            application?.applicant?.user?.sia_certificates?.[0]?.proof_of_address == 1 && (
                                              <View className='gap-2'>
                                                <Button
                                                  size='small'
                                                  rounded={true}
                                                  elevated={false}
                                                  className='mr-[5px] max-w-xs overflow-hidden  truncate border !bg-[#7CDBEF] px-2 py-1 text-xs text-[#323c58] '
                                                >
                                                  <Text className='rubik flex items-center gap-1 text-[12px] font-medium text-[#323c58]'>
                                                    <span className='material-icons text-[16px] text-[#323c58]'>security</span>
                                                    BS7858
                                                  </Text>
                                                </Button>
                                              </View>
                                            )}
                                          {application?.applicant?.user?.surely_pro_badge?.some(
                                            (badge: any) => badge.type === 'InclusivityPledge',
                                          ) && (
                                            <Button
                                              size='small'
                                              variant='outline'
                                              className='border-dark-gradient flex w-[170px] items-center justify-center gap-1 self-stretch rounded-full border !bg-[#ffff] '
                                              icon={() => <img alt={''} src={inclusivitypledgeicon} className='w-[20px]' />}
                                            >
                                              <Text className='rubik text-[12px] font-medium not-italic leading-5 '>Inclusivity Pledge</Text>
                                            </Button>
                                          )}
                                        </View>
                                        <Divider className='mt-[20px] h-[1px] w-full' />
                                      </div>
                                      <div>
                                        <Text className='mt-[10px] break-all text-[14px] font-normal leading-[20px] text-[#383838]'>
                                          {application?.notes?.length > 150
                                            ? `${application?.notes.substring(0, 150)}...`
                                            : application?.notes ?? 'No notes provided'}
                                        </Text>

                                        <button
                                          onClick={() => handleModal(application.applicant)}
                                          className='btn-no-hover mt-[8px] flex h-[32px] w-[134px] !text-[#0B80E7] '
                                        >
                                          <Text className=' flex-start rubik text-[16px] font-medium leading-[24px]'>Read more</Text>
                                        </button>

                                        <Divider className='mt-[10px] h-[1px] w-full' />
                                        {application.status == 'pending' ? (
                                          <div className='mt-[15px] flex flex-row gap-[16px]'>
                                            <Button
                                              variant='outline'
                                              icon={() => <span className='material-icons -mt-1 text-[#CE0E0E]'>clear</span>}
                                              onClick={() => {
                                                declineApplication(application?.applicant?.user?.id);
                                              }}
                                              className='border-neutral flex h-[48px] items-center justify-center gap-2 rounded-[8px] border !border-[#DFE2EA] !bg-[#fff] px-4 py-2 sm:w-[124px]'
                                            >
                                              <Text className='rubik text-[16px] font-medium text-[#CE0E0E]'>Decline</Text>
                                            </Button>
                                            <Button
                                              onClick={() => {
                                                acceptApplication(application?.applicant?.user?.id);
                                              }}
                                              variant='outline'
                                              className='flex h-[48px] items-center justify-center self-stretch rounded-[8px]  !bg-[#0B80E7] sm:w-[124px]'
                                            >
                                              <Text className='rubik text-[16px] font-medium text-[#FFFF]'>Accept</Text>
                                            </Button>
                                          </div>
                                        ) : (
                                          <div className='mx-auto mt-[15px] flex justify-center'>
                                            <ApplicationStatus status={application?.status} />
                                          </div>
                                        )}
                                      </div>
                                    </View>
                                  </Card>
                                </Badge.Container>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    )}
                    {!isLoadingApplications && applicantsData?.length === 0 && (
                      <div className='absolute left-0 right-0 top-10 mx-auto'>
                        <NoOperatorApplied />
                      </div>
                    )}
                  </View>
                </View>
              )}

              {showModal && (
                <ConfirmationModal
                  active={showModal}
                  deactivate={() => setShowModal(false)}
                  successAction={declineApplicationApproved}
                  successLabel='Decline'
                  title='Decline Applicant'
                  description='Are you sure to decline this applicant'
                />
              )}
              {/* {selectedOperator && <AppliedOperatorProfileModal active={active} deactivate={deactivate} selectedOperator={selectedOperator} />} */}
            </Tabs.Panel>
          </View.Item>
        </Tabs>
      </div>
    </div>
  );
};

export default ManageJobsPage;
