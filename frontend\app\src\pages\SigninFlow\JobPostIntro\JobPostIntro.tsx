// @ts-nocheck
import React, { useState, useEffect } from 'react';
import { useToast, useToggle } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import { format, parseISO } from 'date-fns';
import CalendarPostJobNew from 'src/components/Calendars/CalendarPostJobNew';
import { addPostJob } from 'src/services/jobs';
import { getCities } from 'src/services/settings';
import { Image } from 'reshaped';
import surleyicon from '../../../assets/icons/surleyicon/surleyicon.png';

// Import components
import {
  CounterInput,
  FormInput,
  FormSelect,
  PrimaryButton,
  SecondaryButton,
  StepperDots,
  LogoIcon,
  HeaderLogo,
  TitleHeader,
  SubtitleText
} from './components';

// Import styles
import {
  containerStyles,
  headerStyles,
  mainLayoutStyles,
  leftColumnStyles,
  rightColumnStyles,
  formContainerStyles,
  formHeadingStyles,
  centerContentStyles,
  brandLogoStyles,
  logoStyles,
  subHeadingStyles,
  footerStyles,
  actionContainerStyles,
  finalStepTextStyles,
  rateInputStyles,
  labelStyles
} from './styles';

// Import assets
import caretDown from '../../../assets/images/job-post-intro/caret-down.svg';
import addPlus from '../../../assets/images/job-post-intro/add-plus.svg';
import calendarDays from '../../../assets/images/job-post-intro/calendar-days.svg';
import surelyBrand from '../../../assets/images/job-post-intro/brand.png';
import surelyLogo from '../../../assets/images/job-post-intro/surely-logo.png';

const JobPostIntro: React.FC = () => {
  const navigate = useNavigate();
  const toast = useToast();
  const [location, setLocation] = useState('');
  const [securityType, setSecurityType] = useState('');
  const [operatives, setOperatives] = useState(1);
  const [selectedDates, setSelectedDates] = useState([]);
  const [rate, setRate] = useState('');
  
  // Calendar state management
  const { active: calendarActive, activate: activateCalendar, deactivate: deactivateCalendar } = useToggle(false);
  const [closePopovers, setClosePopovers] = useState(false);
  
  // City lookup state
  const [adminDistrict, setAdminDistrict] = useState('');
  const [region, setRegion] = useState('');
  const [country, setCountry] = useState('');
  const [displayLocation, setDisplayLocation] = useState('');

  // Security types options (matching PostJob SIA license options)
  const securityTypes = [
    { value: 'Close Protection', label: 'Close Protection' },
    { value: 'Door Supervisor', label: 'Door Supervisor' },
    { value: 'Security Guard', label: 'Security Guard' },
    { value: 'Public Space Surveillance', label: 'Public Space Surveillance' },
    { value: 'Cash & Valuables in Transit', label: 'Cash & Valuables in Transit' },
    { value: 'Vehicle Immobilisation', label: 'Vehicle Immobilisation' },
  ];

  // Calendar event handlers
  const handleClosePopovers = async () => setClosePopovers(prevState => !prevState);
  
  const handleDateRangeSelect = (dates) => {
    if (dates?.length > 0) {
      setSelectedDates(dates);
    } else if (dates?.start) {
      setSelectedDates([dates]);
    }
  };

  // Operatives handlers (matching PostJob functionality)
  const handleDecreaseOperatives = () => {
    const newValue = parseInt(operatives as string, 10) - 1;
    setOperatives(newValue === 0 ? 1 : newValue); // Minimum 1 operative
  };

  const handleIncreaseOperatives = () => {
    const newValue = parseInt(operatives as string, 10) + 1;
    setOperatives(newValue);
  };

  const handleNumOperativesChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    const numericValue = parseInt(value.replace(/[^0-9]/g, ''), 10);
    setOperatives(isNaN(numericValue) || numericValue === 0 ? 1 : numericValue);
  };

  // Handle location input change
  const handleLocationChange = (e) => {
    const postcode = e.target.value;
    setLocation(postcode);
  };

  // Postal code lookup useEffect (similar to CrucialData)
  useEffect(() => {
    if (location) {
      const formattedPostCode = location.replace(/\s/g, '');
      getCities(formattedPostCode).then((data: any) => {
        if (data) {
          setAdminDistrict(data.admin_district || '');
          setRegion(data.region || '');
          setCountry(data.country || '');
          
          // Create display text: "B1 1DB - Birmingham, West Midlands, England"
          const cityInfo = [data.admin_district, data.region, data.country]
            .filter(Boolean)
            .join(', ');
          
          if (cityInfo) {
            setDisplayLocation(`${location} - ${cityInfo}`);
          } else {
            setDisplayLocation(location);
          }
        } else {
          setDisplayLocation(location);
        }
      }).catch(() => {
        setDisplayLocation(location);
      });
    } else {
      setDisplayLocation('');
      setAdminDistrict('');
      setRegion('');
      setCountry('');
    }
  }, [location]);

  // Format selected dates for display (similar to PostJob)
  const getDateDisplayText = () => {
    if (selectedDates.length === 0) {
      return 'Select a date and time for your shift';
    }
    
    const sortedDates = [...selectedDates].sort((a, b) => new Date(a.start) - new Date(b.start));
    
    if (sortedDates.length === 1) {
      return `${format(parseISO(sortedDates[0].start), 'd MMM yyyy')}`;
    } else {
      return `From ${format(parseISO(sortedDates[0].start), 'd MMM yyyy')} to ${format(parseISO(sortedDates[sortedDates.length - 1].end), 'd MMM yyyy')}`;
    }
  };

  // Submit job data directly to backend
  const submitJobToBackend = async () => {
    try {
      // Map JobPostIntro fields to backend API format
      const jobData = {
        images: [], // No images in JobPostIntro
        jobPostName: `${securityType} - ${location}`, // Generate a job post name
        selectedDates: selectedDates,
        siaLicence: securityType, // Map security type to SIA licence
        industrySectors: [], // Will be handled in future page
        qualifications: [], // Will be handled in future page
        surelyProBadge: [], // Will be handled in future page
        ratingLevel: 0, // Will be handled in future page
        hourlyRateMin: rate || '0',
        hourlyRateMax: rate || '0',
        jobTitle: `${securityType} Required`, // Generate job title
        numOperatives: operatives,
        location: location,
        jobDescription: `We are looking for ${operatives} ${securityType.toLowerCase()} operative${operatives > 1 ? 's' : ''} for ${location}.`, // Generate basic description
        dutyOfCare: '', // Will be handled in future page
        jobBenefits: '', // Will be handled in future page
        isEmergencyHire: false, // Default value
        isInclusivityPledge: false, // Default value
        payment: 10, // Default payment terms
      };

      const response = await addPostJob(jobData);
      
      if (response.error) {
        toast.show({
          title: 'Error',
          text: response.message || 'An error occurred while posting the job.',
          startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
        });
        return false;
      } else {
        toast.show({
          title: 'Success!',
          text: 'Your job has been posted successfully.',
          startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
        });
        // Navigate to manage jobs or success page
        navigate('/manage-jobs/' + response?.id);
        return true;
      }
    } catch (error) {
      toast.show({
        title: 'Error',
        text: error.message || 'An error occurred while posting the job.',
      });
      return;
    }

    if (!securityType) {
      toast.show({
        title: 'Security type is required',
        description: 'Please select a security type.',
        variant: 'critical',
      });
      return;
    }

    if (selectedDates.length === 0) {
      toast.show({
        title: 'Date is required',
        description: 'Please select at least one date.',
        variant: 'critical',
      });
      return;
    }

    if (!rate || parseFloat(rate) <= 0) {
      toast.show({
        title: 'Rate is required',
        description: 'Please enter a valid hourly rate.',
        variant: 'critical',
      });
      return;
    }

    // Store job data in localStorage for JobPostIntroTwo
    const jobData = {
      location,
      securityType,
      operatives,
      selectedDates,
      rate
    };
    localStorage.setItem('jobPostData', JSON.stringify(jobData));
    
    // Navigate to JobPostIntroTwo
    navigate('/job-post-intro-two');
  };

  const handleContinue = () => {
    // Validation
    if (!location.trim()) {
      toast.show({
        title: 'Location is required',
        description: 'Please enter a location for the job.',
        variant: 'critical',
      });
      return;
    }

    if (!securityType) {
      toast.show({
        title: 'Security type is required',
        description: 'Please select a security type.',
        variant: 'critical',
      });
      return;
    }

    if (selectedDates.length === 0) {
      toast.show({
        title: 'Date is required',
        description: 'Please select at least one date.',
        variant: 'critical',
      });
      return;
    }

    if (!rate || parseFloat(rate) <= 0) {
      toast.show({
        title: 'Rate is required',
        description: 'Please enter a valid hourly rate.',
        variant: 'critical',
      });
      return;
    }

    // Store job data in localStorage for JobPostIntroTwo
    const jobData = {
      location,
      securityType,
      operatives,
      selectedDates,
      rate
    };
    localStorage.setItem('jobPostData', JSON.stringify(jobData));
    
    // Navigate to JobPostIntroTwo
    navigate('/job-post-intro-two');
  };

  return (
    <div style={containerStyles}>
      {/* Header */}
      <header style={headerStyles}>
        <HeaderLogo />
      </header>

      {/* Main Layout */}
      <div style={mainLayoutStyles}>
        {/* Left Panel */}
        <div style={leftColumnStyles}>
          <div style={centerContentStyles}>
            <LogoIcon src={surelyLogo} alt="Surely Logo" style={logoStyles} />
            <TitleHeader>
              Let's get you started
            </TitleHeader>
            <SubtitleText>
              Tell us what you need and we will take care of the rest
            </SubtitleText>
            <StepperDots totalSteps={2} currentStep={1} />
          </div>
        </div>

        {/* Right Panel */}
        <div style={rightColumnStyles}>
          <form style={formContainerStyles}>
            

            {/* Where is the job? */}
            <FormInput
              label="Where is the job?"
              value={displayLocation || location}
              onChange={handleLocationChange}
              placeholder="Enter postcode"
            />

            {/* What kind of security do you need? */}
            <FormSelect
              label="What kind of security do you need?"
              value={securityType}
              onChange={(e) => setSecurityType(e.target.value)}
              options={securityTypes}
              placeholder="Select one of the options"
              caretIcon={caretDown}
            />

            {/* How many operatives do you need? */}
            <CounterInput
              label="How many operatives do you need?"
              value={operatives}
              onIncrement={handleIncreaseOperatives}
              onDecrement={handleDecreaseOperatives}
              onChange={handleNumOperativesChange}
            />

            {/* When do you need cover? */}
            <FormInput
              label="When do you need cover?"
              value={getDateDisplayText()}
              onChange={() => {}} // Read-only, calendar handles the changes
              placeholder="Select a date and time for your shift"
              icon={calendarDays}
              onClick={activateCalendar}
              readOnly
              style={{ cursor: 'pointer' }}
            />

            {/* What is the hourly rate? */}
            <div>
              <label style={labelStyles}>
                What is the hourly rate?
              </label>
              <div style={{ position: 'relative' }}>
                <style>{
                  `.rate-input::placeholder {
                    font-family: 'Rubik';
                    font-weight: 300;
                    font-style: normal;
                    font-size: 16px;
                    line-height: 100%;
                    letter-spacing: 0%;
                    opacity: 1;
                  }`
                }</style>
                <input
                  type="number"
                  placeholder="Set a rate"
                  value={rate}
                  onChange={(e) => setRate(e.target.value)}
                  style={rateInputStyles}
                  className="rate-input"
                />
              </div>
            </div>
          </form>
        </div>
      </div>

      {/* Footer */}
      <footer style={footerStyles}>
        <SecondaryButton onClick={() => navigate('/client-dashboard?refresh=true')}>
          Not ready yet
        </SecondaryButton>
        
        <div style={actionContainerStyles}>
          <PrimaryButton onClick={handleContinue}>
            Next
          </PrimaryButton>
          <span style={finalStepTextStyles}>
            Final step coming up
          </span>
        </div>
      </footer>
      
      {/* Calendar Modal */}
      <CalendarPostJobNew
        handleClosePopovers={handleClosePopovers}
        closePopovers={closePopovers}
        active={calendarActive}
        deactivate={() => handleClosePopovers().then(() => deactivateCalendar())}
        handleParentDates={handleDateRangeSelect}
      />
    </div>
  );
};

export default JobPostIntro;
