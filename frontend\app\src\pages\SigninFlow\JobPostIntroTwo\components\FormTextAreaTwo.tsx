import React from 'react';
import { labelStyles } from '../../JobPostIntro/styles';

interface FormTextAreaTwoProps {
  label: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  placeholder?: string;
  rows?: number;
}

const FormTextAreaTwo: React.FC<FormTextAreaTwoProps> = ({
  label,
  value,
  onChange,
  placeholder = '',
  rows = 4
}) => {
  const textareaContainerStyles: React.CSSProperties = {
    marginTop: '14px',  
    marginBottom: '24px',
  };

  const textareaStyles: React.CSSProperties = {
    width: '590px',
    height: '147px',
    padding: '16px',
    borderRadius: '30px',
    border: '1px solid rgba(153, 156, 156, 1)',
    backgroundColor: 'rgba(250, 250, 250, 1)',
    fontSize: '16px',
    fontFamily: 'Rubik, sans-serif',
    resize: 'none',
    boxSizing: 'border-box',
  };

  return (
    <div style={textareaContainerStyles}>
      <label style={labelStyles}>
        {label}
      </label>
      <div style={{ position: 'relative', marginTop: '8px' }}>
        <textarea
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          style={textareaStyles}
          rows={rows}
        />
      </div>
    </div>
  );
};

export default FormTextAreaTwo;
