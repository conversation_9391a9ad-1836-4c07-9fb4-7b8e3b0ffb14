// Corrected PrimaryButton.ts
import React from 'react';

interface PrimaryButtonProps {
  children: React.ReactNode;
  onClick: () => void;
}

const PrimaryButton: React.FC<PrimaryButtonProps> = ({ children, onClick }) => {
  return (
    <button
      type="button"
      onClick={onClick}
      style={{
        width: 126,
        height: 65,
        borderRadius: 20,
        backgroundColor: '#1A1A1A',
        opacity: 1,
        fontFamily: 'Rubik',
        fontWeight: 500,
        fontSize: '16px',
        lineHeight: '100%',
        letterSpacing: '0%',
        color: 'white',
        border: 'none',
        cursor: 'pointer'
      }}
    >
      {children}
    </button>
  );
};

export default PrimaryButton;