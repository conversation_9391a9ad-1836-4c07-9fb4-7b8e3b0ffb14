// @ts-nocheck
import React, { useState } from 'react';
import { Modal, Image, Text, View, Button, Divider, Icon } from 'reshaped';

import { useNavigate } from 'react-router-dom';
import { IdVerified } from 'src/assets/icons';
import inclusivitypledgeicon from '../../../assets/icons/inclusivitypledgeicon/inclusivitypledgeicon.svg';
import '../../../components/Header/HeaderMenu/HeaderMenu.css';
import clientdropdownicon2 from '../../../assets/icons/clientdropdownicon/clientdropdownicon2.svg';

interface AppliedOperatorProfileModalProps {
  active: boolean;
  deactivate: () => void;
  selectedOperator: any | null;
}

const AppliedOperatorProfileModal: React.FC<AppliedOperatorProfileModalProps> = ({ active, deactivate, selectedOperator }) => {
  const navigate = useNavigate();
  const onlycity = selectedOperator?.user?.address_2?.split(', ', 1).pop();

  const baseURL = 'https://app.surelysecurity.com/storage/';

  return (
    <Modal active={active} onClose={deactivate} className='!w-[424px] p-0  '>
      {selectedOperator && (
        <View>
          <View className='flex flex-row-reverse justify-between'>
            <View className='pr-[12px] pt-[12px]'>
              <button onClick={deactivate} className='btn-no-hover ml-auto flex items-start justify-end p-[14px]'>
                <span className='material-icons text-500 align-middle'>close</span>
              </button>
            </View>
            <View className='flex items-center pl-[24px] pt-[24px]'>
              <Text className='rubik py-[16px] text-[24px] font-normal leading-[32px] text-[#323C58]'>Application details</Text>
            </View>
          </View>
          <View className=' flex flex-col gap-[16px] p-[24px] '>
            <View className='flex flex-col gap-[20px]'>
              <div className='flex flex-row items-center gap-4'>
                <View>
                  {selectedOperator.user.profile_photo ? (
                    <div
                      className='rounded-full p-0.5'
                      style={{
                        background: 'linear-gradient(180deg, rgba(50, 60, 88, 0.855), rgba(113, 225, 248, 0.9025))',
                      }}
                    >
                      <Image
                        className='flex h-[80px] w-[80px] flex-col items-start rounded-full bg-white'
                        alt='Profile'
                        src={selectedOperator?.user?.profile_photo?.startsWith(baseURL) ? selectedOperator?.user?.profile_photo : baseURL + selectedOperator?.user?.profile_photo}
                      />
                    </div>
                  ) : (
                    <View className='flex h-[80px]  w-[80px] items-center justify-center  rounded-full bg-[#0B80E7]'>
                      <Text className='rubik text-[30px] text-white'>{selectedOperator.user.name?.charAt(0)}</Text>
                    </View>
                  )}
                  <Icon className='absolute bottom-0 right-0 h-[31px] w-[30px]' svg={IdVerified} />
                </View>
                <div className='flex flex-col justify-start gap-1'>
                  {selectedOperator?.user?.overall_ratings?.count > 0 && (
                    <div className='flex flex-row gap-2'>
                      <span className='material-icons text-[20px] text-[#F4BF00]'>star</span>
                      <Text className='rubik text-[15px] font-medium leading-[20px] text-[#323c58]'>
                        {selectedOperator.user.overall_ratings.value} ({selectedOperator.user.overall_ratings.count})
                      </Text>
                    </div>
                  )}
                  <div>
                    <Text className='rubik text-[16px] font-medium leading-[20px] text-[#1A1A1A]'>{selectedOperator.user.name}</Text>
                  </div>
                  <div className='flex items-center gap-1'>
                    {selectedOperator.user.address_2 ? (
                      <>
                        <Text className='rubik text-line-height text-[15px] font-normal leading-5   text-blue-400 '>{onlycity}</Text>
                        <span className='material-icons-outlined icon-line-height text-[15px] text-blue-400'>place</span>
                      </>
                    ) : (
                      <></>
                    )}
                  </div>
                </div>
              </div>
              {/* <View className='flex flex-col items-start'>
                <Text className='rubik mr-[0px] font-medium  text-[#1A1A1A]'>Jobs completed: {selectedOperator?.user?.jobs?.completed_jobs}</Text>
              </View> */}
              <View className='flex flex-wrap gap-2'>
                {selectedOperator.user.sia_licence_types?.map((grade: any, index: any) => (
                  <Button
                    size='small'
                    key={index}
                    rounded={true}
                    elevated={false}
                    className='max-w-xs overflow-hidden truncate border border-[#05751F] !bg-[#E6FEF3] px-2 py-1 text-xs text-[#323c58]'
                  >
                    <Text color='positive' className='rubik flex items-center gap-1'>
                      <span className='material-icons text-[16px]'>star</span>
                      {grade}
                    </Text>
                  </Button>
                ))}
                {selectedOperator?.sia_certificates?.[0]?.sia_licence === 1 &&
                  selectedOperator?.sia_certificates?.[0]?.id_check === 1 &&
                  selectedOperator?.sia_certificates?.[0]?.employment_history === 1 &&
                  selectedOperator?.sia_certificates?.[0]?.no_criminal_record === 1 &&
                  selectedOperator?.sia_certificates?.[0]?.credit_check === 1 &&
                  selectedOperator?.sia_certificates?.[0]?.proof_of_address === 1 && (
                    <View className='gap-2'>
                      <Button
                        size='small'
                        rounded={true}
                        elevated={false}
                        className='mr-[5px] max-w-xs overflow-hidden  truncate border !bg-[#7CDBEF] px-2 py-1 text-xs text-[#323c58] '
                      >
                        <Text className='rubik flex items-center gap-1 text-[12px] font-medium text-[#323c58]'>
                          <span className='material-icons text-[16px] text-[#323c58]'>security</span>
                          BS7858
                        </Text>
                      </Button>
                    </View>
                  )}
                {selectedOperator?.user?.industry_sectors?.map((industrySector: any, index: any) => (
                  <Button
                    key={index}
                    size='small'
                    rounded={true}
                    elevated={false}
                    className='max-w-xs overflow-hidden truncate border !bg-[#323C58] px-[8px] py-[4px] '
                  >
                    <Text className='rubik text-[12px] font-normal leading-[20px]  text-[#FFFFFF]'>{industrySector}</Text>
                  </Button>
                ))}
                {selectedOperator?.user?.sia_certificates?.[0]?.sia_licence === 1 &&
                  selectedOperator?.user?.sia_certificates?.[0]?.id_check === 1 &&
                  selectedOperator?.user?.sia_certificates?.[0]?.employment_history === 1 &&
                  selectedOperator?.user?.sia_certificates?.[0]?.no_criminal_record === 1 &&
                  selectedOperator?.user?.sia_certificates?.[0]?.credit_check === 1 &&
                  selectedOperator?.user?.sia_certificates?.[0]?.proof_of_address === 1 && (
                    <View className='gap-2'>
                      <Button
                        size='small'
                        rounded={true}
                        elevated={false}
                        className='mr-[5px] max-w-xs overflow-hidden  truncate border !bg-[#7CDBEF] px-2 py-1 text-xs text-[#323c58] '
                      >
                        <Text className='rubik flex items-center gap-1 text-[12px] font-medium text-[#323c58]'>
                          <span className='material-icons text-[16px] text-[#323c58]'>security</span>
                          BS7858
                        </Text>
                      </Button>
                    </View>
                  )}
                {selectedOperator?.user?.surely_pro_badges?.some((badge: any) => 
                  badge.text?.toLowerCase() === 'inclusivity pledge'
                ) && (
                  <Button
                    size='small'
                    variant='outline'
                    className='border-dark-gradient flex w-[170px] items-center justify-center gap-1 self-stretch rounded-full border !bg-[#ffff]'
                    icon={() => <img src={inclusivitypledgeicon} className='w-[20px]' />}
                  >
                    <Text className='rubik text-[12px] font-medium not-italic leading-5'>Inclusivity Pledge</Text>
                  </Button>
                )}
              </View>
              <Divider className='h-[1px] w-full' />
              <Text className='text-[14px] font-normal rubik leading-[20px] text-[#383838]'>{selectedOperator?.notes ?? 'No notes provided'}</Text>

              <Divider className='h-[1px] w-full ' />
            </View>
            <View className='flex flex-row items-start gap-[16px]'>
              <button
                onClick={() => navigate(`/operator-profile/${selectedOperator.user.id}`)}
                className='btn-no-hover border-neutral bg-background-base flex items-center rubik  justify-center rounded border p-[4px] text-[16px] font-medium leading-5  !text-[#0B80E7] '
              >
                View full profile
              </button>
              <button
                onClick={() => {
                  navigate('/chat');
                }}
                className='btn-no-hover rubik border-neutral bg-background-base flex items-center justify-center gap-1 rounded border p-[4px] text-[16px] font-medium leading-6  !text-[#323C58]  '
              >
                <img src={clientdropdownicon2} />
                Chat
              </button>
            </View>
          </View>
        </View>
      )}
    </Modal>
  );
};

export default AppliedOperatorProfileModal;
