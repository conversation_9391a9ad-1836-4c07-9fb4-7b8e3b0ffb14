// @ts-nocheck
import React from 'react';
import { useNavigate } from 'react-router-dom';

// Import dedicated components for JobPostIntroThree
import TitleHeaderThree from './components/TitleHeaderThree';
import HeaderLogoThree from './components/HeaderLogoThree';
import LogoIconThree from './components/LogoIconThree';
import OptionCard from './components/OptionCard';
import RelaxIllustration from './components/RelaxIllustration';
import PlanIllustration from './components/PlanIllustration';

// Import styles from JobPostIntro
import {
  containerStyles,
  headerStyles,
  centerContentStyles,
} from '../JobPostIntro/styles';

const JobPostIntroThree: React.FC = () => {
  const navigate = useNavigate();

  const handleGetMatches = () => {
    navigate('/job-post-intro');
  };

  const handleViewApplications = () => {
    navigate('/client-dashboard');
  };

  return (
    <div style={containerStyles}>
      {/* Header */}
      <header style={headerStyles}>
        <HeaderLogoThree />
      </header>

      {/* Main Content */}
      <main style={{
        ...centerContentStyles,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        flex: 1,
        padding: '40px 20px',
        maxWidth: '1200px',
        margin: '0 auto',
        width: '100%'
      }}>
        {/* Title */}
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%', marginBottom: '10px' }}>
          <TitleHeaderThree />
          <LogoIconThree />
        </div>

        {/* Option Cards */}
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          flexWrap: 'nowrap',
          width: '100%',
          position: 'relative',
          gap: '40px'
        }}>
          <OptionCard
            illustration={<PlanIllustration />}
            title="Relax"
            buttonText="Get Matches"
            description="We’ll shortlist the top 5 matches for you — no stress. Just review, choose who feels right, and confirm the booking. Quick, simple, and handled — so you can focus on everything else."
            position="left"
            onButtonClick={handleGetMatches}
          />
          
          <OptionCard
            illustration={<RelaxIllustration />}
            title="Plan"
            buttonText="View Applications"
            description="Browse every application, ask follow-up questions, and handpick the right person for the job. It’s the best way to make sure you feel completely confident in your choice."
            position="right"
            onButtonClick={handleViewApplications}
          />
        </div>
      </main>
    </div>
  );
};

export default JobPostIntroThree;
