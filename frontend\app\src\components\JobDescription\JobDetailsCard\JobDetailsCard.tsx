// @ts-nocheck
import { useContext, useEffect, useState } from 'react';
import { Card, View, Divider, Button, Text, useToggle, Image } from 'reshaped';
import { useToastSystem } from 'src/context/ToastSystemContext';
import { useNavigate } from 'react-router-dom';
import ApplyNowModal from '../ApplyNowModal/ApplyNowModal';
import { AuthContext } from 'src/context/AuthContext';
import Loading from 'src/components/Loading/Loading';
import inclusivitypledgeicon from '../../../assets/icons/inclusivitypledgeicon/inclusivitypledgeicon.svg';
import moment from 'moment';
import JobShiftModal from 'src/components/JobShiftModal/JobShiftModal';
import { JobContext } from 'src/context/JobContext';
import { format, isValid, parseISO } from 'date-fns';
import surelyproicon1 from '../../../assets/icons/surelyproicon/surelyproicon1.svg';
import surelyproicon2 from '../../../assets/icons/surelyproicon/surelyproicon2.svg';
import surelyproicon3 from '../../../assets/icons/surelyproicon/surelyproicon3.svg';
import surelyproicon4 from '../../../assets/icons/surelyproicon/surelyproicon4.svg';
import surelyproicon5 from '../../../assets/icons/surelyproicon/surelyproicon5.svg';
import surelyproicon6 from '../../../assets/icons/surelyproicon/surelyproicon6.svg';
import surelyproicon7 from '../../../assets/icons/surelyproicon/surelyproicon7.svg';
import { AppContext } from 'src/context/AppContext';
import { getGeneral } from 'src/services/settings';
import surleyicon from '../../../assets/icons/surleyicon/surleyicon.png';

interface JobDetailCardProps {
  selectedJob: any;
}

const JobDetailCard: React.FC<JobDetailCardProps> = ({}) => {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useContext(AuthContext);
  const { hasBankAccount } = useContext(AppContext);
  const toastSystem = useToastSystem();
  const { active: active1, activate: activate1, deactivate: deactivate1 } = useToggle(false);
  const { active: active2, activate: activate2, deactivate: deactivate2 } = useToggle(false);
  const { addFavorite, removeFavorite, selectedJob } = useContext(JobContext);
  const [type, setType] = useState();

  const {
    sia_licence,
    location,
    nr_of_operatives,
    industry_sector,
    is_inclusivity_pledge,
    date_range,
    hourly_rate_min,
    hourly_rate_max,
    updated_at,
    published,
    id,
    is_favorite,
    have_already_applied,
    city,
    post_name,
    title,
    surely_pro_badge,
    escrow_deposit,
    payment_terms,
  } = selectedJob;

  const checkGeneralDetails = async () => {
    try {
      const response = await getGeneral();
      const data = response.data;
      
      // Check if all required general details are present
      const hasName = data.name && data.name.trim() !== '';
      const hasEmail = data.email && data.email.trim() !== '';
      const hasPhone = data.phone && data.phone.trim() !== '';
      const hasAddress = data.address && data.address.trim() !== '';
      const hasPostalCode = data.postal_code && data.postal_code.trim() !== '';
      
      return hasName && hasEmail && hasPhone && hasAddress && hasPostalCode;
    } catch (error) {
      console.error('Error checking general details:', error);
      return false;
    }
  };

  const calculateTotalHours = (dateRanges: any[]): number => {
    let allHours = 0;
    dateRanges.forEach((dateRange) => {
      const start = new Date(dateRange.start);
      const end = new Date(dateRange.end);
      const durationInMilliseconds = end.getTime() - start.getTime();
      const durationInHours = durationInMilliseconds / (1000 * 60 * 60);
      allHours += durationInHours;
    });

    return allHours;
  };

  const totalHours = calculateTotalHours(date_range);
  const getEarningsMin = (totalHours * hourly_rate_min).toFixed(2);
  const getEarningsMax = (totalHours * hourly_rate_max).toFixed(2);

  const geticonSrc = (type: any) => {
    switch (type) {
      case 'Customer Service':
        return surelyproicon3;
      case 'Use Of Equipment':
        return surelyproicon2;
      case 'Disability Focus':
        return surelyproicon6;
      case 'Substance Awareness':
        return surelyproicon4;
      case 'Vulnerable People':
        return surelyproicon5;
      case 'Conflict Managament':
        return surelyproicon7;
      default:
        return surelyproicon1;
    }
  };
  const imageSrc = geticonSrc(type);

  const [hasApply, setHasApply] = useState(false);
  const [isFavourite, setIsFavourite] = useState(is_favorite);
  const [isApplying, setIsApplying] = useState(false);

  const toggleFavorites = () => {
    setIsFavourite((prevFavourite: any) => !prevFavourite);
    if (isFavourite) {
      removeFavorite(id);
    } else {
      addFavorite(id);
    }
  };

  if (!selectedJob) {
    return <Loading />;
  }

  return (
    <Card className='h-full w-full rounded-lg border border-[#dfe2ea] p-6 drop-shadow-md lg:mt-[67px] '>
      <View className='flex flex-col  gap-5'>
        <View className='flex w-full flex-col justify-start gap-1'>
          <View>
            <Text className='rubik text-[16px] font-medium leading-[20px] text-[#1A1A1A]'>Job Details</Text>
          </View>
          <View>
            <Text className='rubik my-3 text-[26px] font-medium leading-[24px] text-[#323C58]'>
              £{hourly_rate_min} to £{hourly_rate_max}
            </Text>
          </View>
          <View direction='row' gap={1}>
            <Text className='rubik text-sm font-medium leading-[20px] text-[#383838]'>I need:</Text>
            <Text className='rubik text-sm font-medium leading-[20px] text-[#0B80E7]'>
              {/* {sia_licence && sia_licence.join(', ')} */}
              {title}
            </Text>
          </View>
          <View direction={'row'} align={'center'} className='my-1'>
            <span className='material-icons-outlined w-1/12  text-sm text-[#383838]'>calendar_today</span>
            <View className='w-11/12'>
              <Text className='rubik text-[14px] font-normal text-[#383838]'>
                From&nbsp;
                <span className='rubik text-[14px] font-medium text-[#383838]'>
                  {date_range.length > 0 && isValid(parseISO(date_range[0].start)) && format(parseISO(date_range[0].start), 'dd MMM yyyy')}
                </span>
                &nbsp;to&nbsp;
                <span className='rubik text-[14px] font-medium text-[#383838]'>
                  {date_range.length > 0 &&
                    isValid(parseISO(date_range[date_range.length - 1].end)) &&
                    format(parseISO(date_range[date_range.length - 1].end), 'dd MMM yyyy')}
                </span>
              </Text>
              <button className='rubik cursor-pointer bg-transparent p-0 text-[14px] font-normal text-[#383838] underline' onClick={activate1}>
                Check shift details
              </button>
              <JobShiftModal active={active1} deactivate={deactivate1} selectedJob={selectedJob} />
            </View>
            <View className={'my-2'}>
              <Text className='rubik text-sm font-normal leading-5 text-[#383838]'>Published&nbsp;{published}</Text>
            </View>
          </View>
          <View className='flex flex-wrap content-center items-center gap-[12px] self-stretch rounded-[8px] bg-[#F4F5F7] p-[16px]'>
            <View direction={'row'} gap={2}>
              {city ? (
                <View direction={'row'} gap={2} className='items-center'>
                  <span className='material-icons-outlined text-base text-[#3C455D]'>place</span>
                  <Text className='rubik break-all text-sm font-normal leading-5 text-[#3C455D]'>{city}</Text>
                </View>
              ) : (
                <></>
              )}
              <View direction={'row'} align={'center'} gap={2}>
                <span className='material-icons text-base text-[#383838] '>person</span>
                <Text className='rubik text-sm font-normal leading-5 text-[#383838] '>{nr_of_operatives} People</Text>
              </View>
            </View>
            <View direction={'row'} align={'center'} gap={2}>
              <span className='material-icons-outlined text-base text-[#383838] '>credit_card</span>
              <View direction={'row'} gap={1}>
                <Text className='rubik text-sm font-normal leading-5 text-[#383838] '>Potential Earnings:</Text>
                <Text className='rubik text-sm font-medium leading-5 text-[#383838] '>
                  £{getEarningsMin} to £{getEarningsMax}
                </Text>
              </View>
            </View>
            <View direction={'row'} align={'center'} gap={2}>
              <span className='material-icons-outlined text-base text-[#383838] '>lock</span>
              <View direction={'row'} gap={1}>
                <Text className='rubik text-sm font-normal leading-5 text-[#383838] '>Escrow deposit:</Text>
                <Text className='rubik text-sm font-medium leading-5 text-[#383838] '>{escrow_deposit}%</Text>
              </View>
            </View>
            <View direction={'row'} align={'center'} gap={2}>
              <span className='material-icons-outlined text-base text-[#383838] '>payments</span>
              <View direction={'row'} gap={1}>
                <Text className='rubik text-sm font-normal leading-5 text-[#383838] '>Payment terms:</Text>
                <Text className='rubik text-sm font-medium leading-5 text-[#383838] '>{payment_terms ? `${payment_terms} days` : '10 days'}</Text>
              </View>
            </View>
          </View>
        </View>
        <Divider className='h-[1px] w-full'></Divider>
        <View className='flex flex-wrap items-start justify-start gap-2'>
          {Array.isArray(sia_licence) &&
            sia_licence.map((licence, licenceIndex) => (
              <Button
                key={licenceIndex}
                size='small'
                rounded={true}
                elevated={false}
                className=' max-w-xs overflow-hidden truncate border border-[#05751F] !bg-[#E6FEF3] px-2 py-1 text-xs   text-[#323c58]'
              >
                <Text color='positive' className='rubik flex items-center gap-1 '>
                  <span className='material-icons text-[16px]'>star</span>
                  {licence}
                </Text>
              </Button>
            ))}
          {industry_sector?.map((sector: any, sectorIndex: any) => (
            <Button
              key={sectorIndex}
              size='small'
              rounded={true}
              elevated={false}
              className=' max-w-xs overflow-hidden truncate border !bg-[#323C58] px-2 py-1 text-xs'
            >
              <Text className='rubik font-normal leading-4 text-[#FFFFFF]'>{sector}</Text>
            </Button>
          ))}
          {surely_pro_badge
            ?.filter((badge: any) => badge !== 'InclusivityPledge')
            ?.map((badge: any, index: any) => (
              <Button
                key={index}
                size='small'
                rounded={true}
                elevated={false}
                className='max-w-xs overflow-hidden truncate  border !bg-[#DDEFFF] px-2 py-1 text-xs'
              >
                <View className='flex flex-row'>
                  {badge && <img src={geticonSrc(badge)} alt={badge} className='mr-2' />}
                  <Text className='rubik font-normal leading-4 text-[#053D6D]'>{badge}</Text>
                </View>
              </Button>
            ))}

          {is_inclusivity_pledge && (
            <Button
              size='small'
              rounded={true}
              elevated={false}
              variant='outline'
              className='border-dark-gradient max-w-xs truncate border !bg-[#ffff] px-2 py-1 text-xs '
              icon={() => <img src={inclusivitypledgeicon} className='w-[16px]' />}
            >
              <Text className='rubik font-normal leading-4 !text-[#323C58] text-[#FFFFFF]'>Inclusivity Pledge</Text>
            </Button>
          )}
        </View>
        {isAuthenticated ? (
          user.data?.account_type == 2 ? (
            <>
              <View width={'100%'}>
                <Button className='!bg-[#0B80E7] !text-white' fullWidth={true}>
                  Review applicants
                </Button>
              </View>
              <View width={'100%'}>
                <Button variant='outline' className='!text-[#0B80E7]' fullWidth={true} onClick={() => navigate('/post-job')}>
                  <View className='flex flex-row items-center justify-center gap-2 '>
                    <span className='material-icons-outlined'>edit</span>
                    <Text> Edit this job posting</Text>
                  </View>
                </Button>
              </View>
            </>
          ) : (
            <>
              <View width={'100%'}>
                <Button
                  onClick={async () => {
                    // First check if general details are complete
                    const hasGeneral = await checkGeneralDetails();
                    
                    if (!hasGeneral) {
                      toastSystem.showInfo(
                        'Complete Profile Required',
                        'To apply for jobs, please complete your general information first, then add bank details.',
                        {
                          text: 'Complete Profile',
                          onClick: () => navigate('/operator-settings-general', { state: { fromJobApplication: true, returnToJob: selectedJob?.id } })
                        }
                      );
                      return;
                    }

                    // Then check if bank account is set up
                    if (!hasBankAccount) {
                      toastSystem.showInfo(
                        'Bank Account Required',
                        'In order to apply for jobs, you need to add your bank account details first.',
                        {
                          text: 'Add Bank Details',
                          onClick: () => navigate('/operator-settings-payment', {
                            state: {
                              activeTab: '0',
                              returnToJob: selectedJob?.id
                            }
                          })
                        }
                      );
                      return;
                    }
                    setIsApplying(true);
                    activate2();
                  }}
                  className='rounded-[8px] !bg-[#0B80E7] '
                  fullWidth={true}
                  disabled={isApplying || hasApply || have_already_applied}
                >
                  <Text className='rubik text-[16px] font-medium leading-[24px] text-[#FFF]'>
                    {isApplying ? 'Applying...' : hasApply || have_already_applied ? 'Already applied' : 'Apply Now'}
                  </Text>
                </Button>
                <ApplyNowModal
                  active={active2}
                  deactivate={() => {
                    deactivate2();
                    setIsApplying(false);
                  }}
                  selectedJob={selectedJob}
                  onUpdateHasApply={(newHasApply, error) => {
                    if (!error?.includes('Unauthorized user to apply!')) {
                      setHasApply(newHasApply);
                    } else {
                      setHasApply(false);
                    }
                    setIsApplying(false);
                  }}
                />
              </View>
            </>
          )
        ) : (
          <div className=' mr-[30px] flex w-full max-w-[290px] items-center justify-between'></div>
        )}
        <View width={'100%'}>
          <Button onClick={toggleFavorites} className='rounded-[8px] border-[#DFE2EA] !bg-[#fff] !text-[#0B80E7]' fullWidth={true}>
            {!isFavourite && (
              <View className='flex flex-row items-center justify-center gap-2 '>
                <svg xmlns='http://www.w3.org/2000/svg' width='15' height='14' viewBox='0 0 15 14' fill='none'>
                  <path
                    d='M11.0257 0.439941C9.7816 0.439941 8.58755 1.01909 7.8082 1.93429C7.02885 1.01909 5.8348 0.439941 4.5907 0.439941C2.3885 0.439941 0.658203 2.17024 0.658203 4.37244C0.658203 7.07514 3.0892 9.27734 6.77145 12.6235L7.8082 13.5602L8.84495 12.6164C12.5272 9.27734 14.9582 7.07514 14.9582 4.37244C14.9582 2.17024 13.2279 0.439941 11.0257 0.439941ZM7.8797 11.5582L7.8082 11.6297L7.7367 11.5582C4.3333 8.47654 2.0882 6.43879 2.0882 4.37244C2.0882 2.94244 3.1607 1.86994 4.5907 1.86994C5.6918 1.86994 6.7643 2.57779 7.14325 3.55734H8.4803C8.8521 2.57779 9.9246 1.86994 11.0257 1.86994C12.4557 1.86994 13.5282 2.94244 13.5282 4.37244C13.5282 6.43879 11.2831 8.47654 7.8797 11.5582Z'
                    fill='#0B80E7'
                  />
                </svg>
                <Text className='rubik text-[16px] font-medium leading-[24px] text-[#0B80E7]'>Add to favourites</Text>
              </View>
            )}
            {isFavourite && (
              <View className='flex flex-row items-center justify-center gap-2 '>
                <svg xmlns='http://www.w3.org/2000/svg' width='15' height='14' viewBox='0 0 15 14' fill='red'>
                  <path
                    d='M11.0257 0.439941C9.7816 0.439941 8.58755 1.01909 7.8082 1.93429C7.02885 1.01909 5.8348 0.439941 4.5907 0.439941C2.3885 0.439941 0.658203 2.17024 0.658203 4.37244C0.658203 7.07514 3.0892 9.27734 6.77145 12.6235L7.8082 13.5602L8.84495 12.6164C12.5272 9.27734 14.9582 7.07514 14.9582 4.37244C14.9582 2.17024 13.2279 0.439941 11.0257 0.439941Z'
                    fill='red'
                  />
                </svg>
                <Text className='rubik text-[16px] font-medium leading-[24px] text-[#0B80E7]'>Remove from favourites</Text>
              </View>
            )}
          </Button>
        </View>
      </View>
    </Card>
  );
};

export default JobDetailCard;
