// @ts-nocheck
import React, { ChangeEvent, useRef, useState } from 'react';
import { Text, View, Button, Divider, Modal, Image, useToast, Icon } from 'reshaped';
import '../../../../components/Header/HeaderMenu/HeaderMenu.css';
import surleyicon from '../../../../assets/icons/surleyicon/surleyicon.png';
import { IdVerified } from 'src/assets/icons';
import { reportOperative } from 'src/services/operatives';

interface ReportOperatorModalProps {
  active: boolean;
  deactivate: () => void;
  oper: any;
}

const ReportOperatorModal: React.FC<ReportOperatorModalProps> = ({ active, deactivate, oper }) => {
  const toast = useToast();
  const onlycity = oper?.city?.split(', ', 1).pop();
  const [selectedFiles1, setSelectedFiles1] = useState<{ name: string; data: string }[]>([]);
  const [document, setDocument] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [validFileName, setValidFileName] = useState(true);

  const lastname = oper?.name?.split(' ').pop()?.charAt(0).toUpperCase() + oper?.name?.split(' ').pop()?.slice(1);
  const firstname = oper?.name?.split(' ').shift()?.charAt(0).toUpperCase() + oper?.name?.split(' ').shift()?.slice(1);
  const initialName = `${firstname || ''} ${lastname || ''}`;
  const initialNameFirstCharAt = (firstname?.[0] || '') + (lastname?.[0] || '');

  const [details, setDetails] = useState<string>('');

  const readFileAsBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target && typeof e.target.result === 'string') {
          resolve(e.target.result);
        } else {
          reject(new Error('Failed to read file as base64.'));
        }
      };
      reader.readAsDataURL(file);
    });
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files: any = e.target.files;

    if (files && files.length > 0 && selectedFiles1.length < 3) {
      const fileDataArray: { name: string; data: string }[] = [];

      for (const file of files) {
        const base64 = await readFileAsBase64(file);
        fileDataArray.push({ name: file.name, data: base64 });
      }
      const documentDataArray: string[] = fileDataArray.map((file) => file.data);
      setDocument([...document, ...documentDataArray.slice(0, 3 - document.length)]);
      setSelectedFiles1([...selectedFiles1, ...fileDataArray.slice(0, 3 - selectedFiles1.length)]);
    }
  };

  const deleteFile = (indexToDelete: number) => {
    const updatedFiles = selectedFiles1.filter((_, index) => index !== indexToDelete);
    setSelectedFiles1(updatedFiles);
  };

  const openFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleDetailsChange = (event: ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => {
    setDetails(event.target.value);
  };

  const getImageSrc = (images: any) => {
    const baseUrl = 'https://app.surelysecurity.com/storage/';

    if (images && !images.startsWith(baseUrl)) {
      return baseUrl + images;
    }

    return images;
  };

  const reportOperator = () => {
    const report: any = {
      id: oper.id,
      details,
      document,
    };
    reportOperative(report)
      .then((res: any) => {
        toast.show({
          title: 'Done!',
          text: 'You have reported this operator.',
          startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
        });
        deactivate();
      })
      .catch((error) => {
        toast.show({
          title: 'Error',
          text: error.message, 
          startSlot: <Image src={surleyicon} className='h-[30px] w-[30px]' />,
        });
        deactivate();
      });
  };
  

  return (
    <Modal active={active} onClose={deactivate} className='!h-[auto] !w-[424px] p-[24px]'>
      <View className='gap-[16px]'>
        <button onClick={deactivate} className='btn-no-hover ml-auto flex items-center justify-end p-0'>
          <span className='material-icons text-500 align-middle'>close</span>
        </button>
        <View className='flex items-center p-0'>
          <Text className='rubik text-[24px] font-normal leading-[32px] text-[#323C58]'>Report Operator</Text>
        </View>

        <View className='mt-[16px] flex flex-col'>
          <View className='flex flex-row gap-[20px]'>
            <View>
              {oper?.profile_photo ? (
                <div
                  className='rounded-full p-0.5'
                  style={{
                    background: 'linear-gradient(180deg, rgba(50, 60, 88, 0.855), rgba(113, 225, 248, 0.9025))',
                  }}
                >
                  <Image
                    className='flex h-[64px] w-[64px] flex-col items-start rounded-full bg-white'
                    alt='Profile'
                    src={getImageSrc(oper?.profile_photo)}
                  />
                </div>
              ) : (
                <View className='flex h-[64px] w-[64px] items-center justify-center  rounded-full bg-[#C7CDDB]'>
                  <Text className='rubik text-[18px] font-medium text-[#323C58] '>{initialNameFirstCharAt}</Text>
                </View>
              )}
              <Icon className='absolute bottom-0 right-0 h-[31px] w-[30px]' svg={IdVerified} />
            </View>
            <View className='mt-[5px] flex flex-col gap-[4px]'>
              <Text className=' rubik text-[15px] font-normal leading-[20px] text-[#323C58]'>{initialName}</Text>
              {oper?.city ? (
                <div className='flex flex-row gap-2'>
                  <Text className='rubik text-line-height text-[16px] leading-5   text-blue-400 '>{onlycity}</Text>
                  <span className='material-icons-outlined icon-line-height mt-[2px] text-[15px] text-blue-400'>place</span>
                </div>
              ) : (
                <></>
              )}
            </View>
          </View>

          <Text className='rubik mt-[16px] text-[14px] font-medium leading-[20px] text-[#14171F]'>Details</Text>
          <div className='mt-[2px] flex flex flex-col justify-between rounded border border-solid border-gray-300 bg-[#ffff] p-[8px]'>
            <textarea
              name='job_description'
              placeholder='Tell us more about the issue and provide any useful information...'
              className=' border-none  bg-transparent outline-none'
              value={details}
              onChange={handleDetailsChange}
              maxLength={500}
              rows={9}
              style={{
                wordWrap: 'break-word',
                overflowWrap: 'break-word',
                resize: 'none',
              }}
            />

            <p className='mb-[1px] mr-[5px] text-right text-gray-300'>{500 - (details?.length || 0)} characters left</p>
          </div>

          <Text className='rubik mt-[16px] text-[14px] font-medium leading-[20px] text-[#14171F]'>
            Upload useful files (screenshots, photos etc.)
          </Text>

          <Button
            variant='outline'
            icon={() => <span className='material-icons-outlined mt-[-1px] text-[21px] text-[#323C58]'>upload</span>}
            onClick={openFileInput}
            className='border-neutral bg-background-base mt-[16px] flex h-[60px] h-[60px] w-[full] w-full items-center justify-center gap-2 self-stretch self-stretch rounded-[8px] border !bg-[white] px-4 py-2 text-[15px] !text-[#000000]'
            disabled={selectedFiles1.length >= 3}
          >
            <Text className='rubik text-[14px] font-medium leading-[20px] text-[#323C58]'>
              Drop or&nbsp;
              <span className='rubik text-[14px] font-medium leading-[20px] text-[#0B80E7]'>browse</span>
            </Text>
            <input type='file' ref={fileInputRef} accept='.pdf,.doc,.docx,.jpg,.jpeg,.png' style={{ display: 'none' }} onChange={handleFileChange} />
          </Button>
          {!validFileName && <Text className='rubik mt-[16px] text-[15px] font-normal leading-5  text-red-400'>Specified document is required.</Text>}
          <ul>
            {selectedFiles1.map((file: any, index: any) => (
              <li key={index}>
                <View className='mt-[18px] flex flex-row items-center'>
                  <Text className='rubik mt-[5px] text-base font-normal leading-5 text-[#1A1A1A] '>{file.name}</Text>
                  <Button
                    variant='outline'
                    className='ml-[16px] mt-[10px] flex w-[78px] items-center justify-center !border-[#DFE2EA] !bg-[#fff] '
                    onClick={() => deleteFile(index)}
                  >
                    <Text className='rubik p-[4px 8px 4px 8px] flex items-center align-middle text-[14px] font-normal text-[#CB101D]'>
                      <span className='material-icons align-middle text-[17px]'>close</span>
                      Delete
                    </Text>
                  </Button>
                </View>
              </li>
            ))}
          </ul>
          <Text className=' rubik mt-[8px] text-[14px] leading-[20px] text-[#444B5F]'>PDF, JPEG, PNG, MP4, Mov files only.</Text>
        </View>
        <Divider className='mt-[16px] h-[1px] w-full'></Divider>
        <View className='mt-[16px] flex flex-row justify-end'>
          <Button
            variant='outline'
            icon={() => <span className='material-icons -mt-1 text-[#CB101D]'>clear</span>}
            onClick={deactivate}
            className='border-neutral mr-[20px] flex h-[48px] w-[180px] items-center justify-center gap-2 rounded-[8px]  border px-4 py-2'
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] text-[#CB101D]'>Cancel</Text>
          </Button>
          <Button
            onClick={() => {
              reportOperator();
              deactivate();
            }}
            className='border-neutral flex h-[48px] w-[180px] items-center justify-center gap-2 rounded-[8px] border !bg-[#0B80E7] px-4 py-2'
          >
            <Text className='rubik text-[16px] font-medium leading-[24px] text-[#FFFFFF]'>Submit</Text>
          </Button>
        </View>
      </View>
    </Modal>
  );
};

export default ReportOperatorModal;
