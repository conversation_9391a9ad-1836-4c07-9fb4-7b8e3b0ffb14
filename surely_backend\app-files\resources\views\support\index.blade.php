@extends('layouts.app')

@section('title', 'Supports')

@section('head')

    <!-- BEGIN PAGE LEVEL STYLES -->

    <link rel="stylesheet" type="text/css" href="{{asset('plugins/table/datatable/datatables.css')}}">
    <link rel="stylesheet" type="text/css" href="{{asset('plugins/table/datatable/dt-global_style.css')}}">
    <link rel="stylesheet" type="text/css" href="{{asset('plugins/table/datatable/custom_dt_html5.css')}}">

    <!-- END PAGE LEVEL STYLES -->


@endsection

@section('content')
    <!-- BEGIN: Content-->

    <nav class="breadcrumb-one layout-top-spacing" aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/"><i class="fa fa-home"></i> Home</a></li>
            <li class="breadcrumb-item active" aria-current="page">Supports</li>
        </ol>
    </nav>

    <div class="app-content content mt-4">
        <div class="content-overlay"></div>
        <div class="content-wrapper">
            <div class="content-body">
                <!-- Zero configuration table -->
                <section id="configuration">
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-content collapse show">
                                    <div class="card-body card-dashboard">
                                        {!! $dataTable->table(['class' => 'table table-bordered']) !!}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                <!--/ Zero configuration table -->
            </div>
        </div>
    </div>
    
    <!-- Support Details Modal -->
    <div class="modal fade" id="details-modal">
        <div class="modal-dialog modal-lg modal-dialog-centered-md">
            <div class="modal-content">
                <!-- Modal Header -->
                <div class="modal-header">
                    <h4 class="modal-title text-center">Contract Details</h4>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                
                <!-- Modal body -->
                <div class="modal-body">
                    <!-- Content will be loaded dynamically via AJAX -->
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                    </div>
                </div>
                
                <!-- Modal footer -->
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script src="{{asset('plugins/table/datatable/datatables.js')}}"></script>
    <script src="{{asset('plugins/table/datatable/button-ext/dataTables.buttons.min.js')}}"></script>
    <script src="{{asset('plugins/table/datatable/button-ext/jszip.min.js')}}"></script>
    <script src="{{asset('plugins/table/datatable/button-ext/buttons.html5.min.js')}}"></script>
    <script src="{{asset('plugins/table/datatable/button-ext/buttons.print.min.js')}}"></script>
    {{ $dataTable->scripts(attributes: ['type' => 'module']) }}
    
    <script>
        $(document).ready(function() {
            // Listen for clicks on the support details button
            $(document).on('click', '.support-details-btn', function() {
                const supportId = $(this).data('id');
                
                // Show loading state
                $('#details-modal .modal-body').html('<div class="text-center"><div class="spinner-border" role="status"><span class="sr-only">Loading...</span></div></div>');
                
                // Fetch support details via AJAX
                $.ajax({
                    url: `/supports/${supportId}/details`,
                    method: 'GET',
                    success: function(response) {
                        if (!response.error) {
                            const data = response.data;
                            
                            // Update modal content with fetched data
                            updateModalContent(data);
                        } else {
                            $('#details-modal .modal-body').html('<div class="alert alert-danger">Error loading support details</div>');
                        }
                    },
                    error: function() {
                        $('#details-modal .modal-body').html('<div class="alert alert-danger">Error loading support details</div>');
                    }
                });
            });
            
            // Map numeric/string contract status to a human-friendly label
            function mapContractStatus(status) {
                const mapping = {
                    1: 'Invited',
                    2: 'Pending',
                    3: 'In Progress',
                    4: 'Complete',
                    5: 'Canceled',
                    6: 'Rejected',
                    'Invited': 'Invited',
                    'Pending': 'Pending',
                    'In progress': 'In Progress',
                    'In Progress': 'In Progress',
                    'Completed': 'Complete',
                    'Complete': 'Complete',
                    'Canceled': 'Canceled',
                    'Cancelled': 'Canceled',
                    'Rejected': 'Rejected',
                };
                if (Object.prototype.hasOwnProperty.call(mapping, status)) {
                    return mapping[status];
                }
                return (typeof status === 'string' && status.length) ? status : 'Unknown';
            }

            // Function to update modal content with fetched data
            function updateModalContent(data) {
                const statusLabel = mapContractStatus(data.contract_status);
                let modalContent = `
                <div class="container table-responsive">
                    <div class="text-left">
                        <h5>Asked for support: ${data.name}</h5>
                    </div>
                    <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th scope="col" class="col-2">ID # ${data.contract_id}</th>
                                <th scope="col">Name</th>
                                <th scope="col">Email</th>
                                <th class="text-center" scope="col">Phone</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Operative</td>
                                <td>
                                    <span class="table-inner-text text-black">${data.operative_name}</span>
                                </td>
                                <td class="text-center text-black">${data.operative_email}</td>
                                <td class="text-center">
                                    <span class="badge badge-light-success text-black">${data.operative_phone}</span>
                                </td>
                            </tr>
                            <tr>
                                <td>Client</td>
                                <td>
                                    <span class="table-inner-text text-black">${data.client_name}</span>
                                </td>
                                <td class="text-center text-black">${data.client_email}</td>
                                <td class="text-center">
                                    <span class="badge badge-light-secondary text-black">${data.client_phone}</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th scope="col" class="col-2">Hourly Rate</th>
                                <th scope="col">Location</th>
                                <th class="text-center" scope="col">Start Date</th>
                                <th class="text-center" scope="col">End Date</th>
                                <th class="text-center" scope="col">Contract Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="text-black">${data.hourly_rate}</td>
                                <td>
                                    <span class="table-inner-text text-black">${data.contract_location}</span>
                                </td>
                                <td class="text-center text-black">${data.contract_start_date}</td>
                                <td class="text-center">
                                    <span class="badge badge-light-success text-black">${data.contract_end_date}</span>
                                </td>
                                <td class="text-center">
                                    <span class="badge badge-light-secondary text-black">${statusLabel}</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>


                <div class="col border rounded p-2">
                    <h5 class="text-left">Ask For Support Message</h5>
                    <p class="text-left text-black">${data.comment}</p>
                </div>
                </div>
                `;
                
                $('#details-modal .modal-body').html(modalContent);
            }
        });
    </script>
@endsection

