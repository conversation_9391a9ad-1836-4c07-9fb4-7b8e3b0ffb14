<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\GeneralAccountSettingResource;
use App\Http\Resources\ProfileResource;
use App\Mail\SiaLicenceValidation;
use App\Models\MobileUser;
use App\Notifications\SiaLicenceValidationFailedNotification;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use \Illuminate\Support\Facades\Mail;
use Symfony\Component\DomCrawler\Crawler;

class GeneralController extends Controller
{
    public function index(): JsonResponse
    {
        $user = auth()->user();
        $data = new GeneralAccountSettingResource($user);

        return response()->json([
            'error' => false,
            'message' => 'Data retrieved successfully!',
            'data' => $data
        ]);
    }

    public function update(Request $request): JsonResponse
    {
        $user = auth()->user();
        $email = $user->email;

        $data['name'] = $request->name;
        $data['email'] = $request->email;
        $data['phone'] = $request->phone;
        $data['postal_code'] = $request->postal_code;
        $data['address_3'] = $request->address_3;
        $data['location_range'] = $request->location_range;
        $data['address'] = $request->address;
        $data['address_1'] = $request->address_1;
        $data['address_2'] = $request->address_2;

        if (!empty($request->postal_code)) {
            $client = new Client();

            $response = $client->request('GET', "http://api.postcodes.io/postcodes/".$data['postal_code']);
            $dataPostcode = $response->getBody()->getContents();
            $dataPostcode = json_decode($dataPostcode);
            $data['lat'] = $dataPostcode->result->latitude;
            $data['lng'] = $dataPostcode->result->longitude;
        }

        if(! $user->update($data)) {
            return response()->json([
                'error' => true,
                'message' => 'Data cannot updated!',
                'data' => $data,
            ]);
        }

        if ($data['email'] != $email) {
            $response = Http::post("https://grune-oase.com/api/send_verification_email", [
                'ref' => 'login',
                'email' => $data['email'],
            ]);

            $response = json_decode($response);

            if ($response->error) {
                return response()->json($response);
            }

            $user->email_verified = false;
            $user->save();

            return response()->json([
                'error' => false,
                'message' => "Data updated successfully! Check the verification email at $user->email !",
                'data' => $data,
            ]);
        }
        
        return response()->json([
            'error' => false,
            'message' => 'Data updated successfully!',
            'data' => $data,
        ]);
    }

    public function postalCode($postcode) {

        $client = new Client();

        try {
        $response = $client->request('GET', "http://api.postcodes.io/postcodes/$postcode");
        $data = $response->getBody()->getContents();
        $data = json_decode($data);
        $result = $data->result;

        return response()->json([
            'error' => false,
            'message' => 'Data retrieved successfully!',
            'data' => $result,
        ]);
        } catch (RequestException $exception) {
            if ($exception->getResponse()->getStatusCode() == 404) {

                return response()->json([
                    'error' => true,
                    'message' => 'Invalid postcode!',
                    'data' => [],
                ]);
            }
        }
    }

    public function siaLicenceValidation($siaLicenceNumber): ?JsonResponse
    {
        $used = MobileUser::where('sia_licence_number', $siaLicenceNumber)->first();
        
        if ($used) {
            return response()->json([
                'error' => true,
                'message' => 'Sia Licence has already been used in Surely!',
            ]);
        }

        $client = new Client();

        try {
            $response = $client->request('POST', 'https://services.sia.homeoffice.gov.uk/PublicRegister/SearchPublicRegisterByLicence', [
                'multipart' => [
                    [
                        'name' => 'LicenseNo',
                        'contents' => $siaLicenceNumber
                    ],
                ],
            ]);

            $data = $response->getBody()->getContents();

            $crawler = new Crawler($data);
            $panel = $crawler->filter('.panel-body')->first();

            $firstRow = $panel->filter('.row:nth-child(1)')->first();
            $firstName = $firstRow->filter('.col-md-3:nth-child(1)')->filter('.ax_h5')->first()->text();
            $surname = $firstRow->filter('.col-md-3:nth-child(2)')->filter('.ax_h5')->first()->text();

            $secondRow = $panel->filter('.row:nth-child(2)')->first();
            $licenceNumber = $secondRow->filter('.col-md-3:nth-child(1)')->filter('.ax_h4')->first()->text();
            $role = $secondRow->filter('.col-md-3:nth-child(2)')->filter('.ax_h4')->first()->text();
            $licenceSector = $secondRow->filter('.col-md-3:nth-child(3)')->filter('.ax_h4')->first()->text();

            $thirdRow = $panel->filter('.row:nth-child(3)')->first();
            $expiryDate = $thirdRow->filter('.col-md-3:nth-child(1)')->filter('.ax_h4')->first()->text();
            $status = $thirdRow->filter('.col-md-3:nth-child(2)')->filter('.ax_h4_green')->first()->text();

            $response = [
                'firstname' => $firstName,
                'surname' => $surname,
                'licence_number' => $licenceNumber,
                'role' => $role,
                'licence_sector' => $licenceSector,
                'expiry_date' => $expiryDate,
                'status' => $status,
            ];

            return response()->json([
                'error' => false,
                'message' => 'Data retrieved successfully!',
                'data' => $response,
            ]);
        } catch (\InvalidArgumentException $exception) {
            try {
                auth()->user()->notify(new SiaLicenceValidationFailedNotification(auth()->user()->name));
            } catch (Exception $e) {
                //
            }
            return response()->json([
                'error' => true,
                'message' => 'Invalid Sia Licence!',
                'data' => [],
            ]);
        }
    }
}
