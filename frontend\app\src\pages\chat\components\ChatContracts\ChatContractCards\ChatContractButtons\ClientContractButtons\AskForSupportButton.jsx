import { useToggle } from 'reshaped';
import AskForSupportClientModal from '../../../../ChatModals/AskForSupportClientModal';

const ClientAskForSupportButton = ({ operator, chat }) => {
  const { active, activate, deactivate } = useToggle(false);

  return (
    <>
      <div onClick={activate} className='w-full flex cursor-pointer items-center justify-center gap-2 rounded-[8px] border border-[#DFE2EA] py-[10.5px]'>
        <p className='rubik flex items-center gap-2 whitespace-nowrap text-[16px] font-medium leading-[24px] text-[#323C58]'>
          <span className='material-icons'>support</span>
          Support
        </p>
      </div>
      <AskForSupportClientModal active={active} deactivate={deactivate} operator={operator} chat={chat} />
    </>
  );
};

export default ClientAskForSupportButton;
