import React from 'react';

interface OptionCardProps {
  illustration: React.ReactNode;
  title: string;
  buttonText: string;
  description: string;
  position: 'left' | 'right';
  onButtonClick: () => void;
}

const OptionCard: React.FC<OptionCardProps> = ({ 
  illustration, 
  title, 
  buttonText, 
  description,
  position,
  onButtonClick
}) => {
  // Determine button width based on text
  const buttonWidth = buttonText.toLowerCase().includes('view') ? '219px' : '152px';

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      maxWidth: '278px',
      width: '100%',
      gap: '10px',
      position: 'relative',
    }}>
      {/* Description Text */}
      <div style={{
        position: 'absolute',
        top: '47.5%',
        transform: 'translateY(-50%)',
        [position === 'left' ? 'right' : 'left']: 'calc(100% + 1px)',
        width: '195px',
        fontFamily: 'Rubik',
        fontSize: '13px',
        fontWeight: 400,
        fontStyle: 'normal',
        color: 'rgba(0, 0, 0, 1)',
        lineHeight: '150%',
        textAlign: position === 'left' ? 'right' : 'left',
        padding: position === 'left' ? '0 0 0 0' : '0 0 0 0',
        boxSizing: 'border-box'
      }}>
        {description}
      </div>
      {/* Illustration */}
      <div style={{
        width: '278px',
        height: '269px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        flexShrink: 0,
        borderRadius: '30px',
        overflow: 'hidden'
      }}>
        {illustration}
      </div>

      {/* Title */}
      <h2 style={{
        fontFamily: 'Rufina Stencil',
        fontSize: '32px',
        fontWeight: 400,
        fontStyle: 'normal',
        color: '#1A1A1A',
        margin: 0,
        lineHeight: '100%',
        letterSpacing: '0%',
        textAlign: 'center',
        textTransform: 'capitalize'
      }}>
        {title}
      </h2>

      {/* Button */}
      <button
        type="button"
        onClick={onButtonClick}
        style={{
          width: buttonWidth,
          height: '65px',
          borderRadius: '20px',
          backgroundColor: '#1A1A1A',
          fontFamily: 'Rubik',
          fontWeight: 500,
          fontSize: '16px',
          lineHeight: '100%',
          color: 'white',
          border: 'none',
          cursor: 'pointer',
          transition: 'opacity 0.2s',
          opacity: 1
        }}
        onMouseOver={(e) => (e.currentTarget.style.opacity = '0.9')}
        onMouseOut={(e) => (e.currentTarget.style.opacity = '1')}
      >
        {buttonText}
      </button>
    </div>
  );
};

export default OptionCard;
