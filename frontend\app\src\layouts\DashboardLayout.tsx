// @ts-nocheck
import React, { useContext, useEffect } from 'react';
import { Container } from 'reshaped';
import { useNavigate } from 'react-router-dom';
import { useToastSystem } from 'src/context/ToastSystemContext';

import Header from '../components/Header/Header';
import ClientSearchBarTwo from 'src/components/ClientSearchBar/ClientSearchBarTwo';
import { AppContext } from 'src/context/AppContext';
import surleyicon from '../assets/icons/surleyicon/surleyicon.png';
import { EmailVerificationBanner } from 'src/components/EmailVerificationBanner';

interface Props {
  children?: React.ReactNode;
}

export const DashboardLayout = ({ children }: Props): JSX.Element => {
  const { clientRepresentBusinessAs, companyRegisteredNumber, hasCreditCard } = useContext(AppContext);
  const navigate = useNavigate();
  const toastSystem = useToastSystem();
  const isCreditCardUnavailable = hasCreditCard === null || hasCreditCard === undefined;
  const isDataUnavailable = clientRepresentBusinessAs === null || clientRepresentBusinessAs === undefined;

  useEffect(() => {
    if (isCreditCardUnavailable) {
      toastSystem.showWelcome(
        'In order to start hiring, you will need to fill in your card details in the payment settings section.',
        {
          text: 'Proceed to settings',
          onClick: () => navigate('/client-settings-payment', { state: { activeTab: '2' } })
        }
      );
    }
  }, [isCreditCardUnavailable, navigate]);

  useEffect(() => {
    if (isDataUnavailable) {
      toastSystem.showWelcome(
        'Complete your company validation to start hiring security operatives. Please provide your business details to get started.',
        {
          text: 'Validate your profile',
          onClick: () => navigate('/first-step-validation-client')
        }
      );
    }
  }, [isDataUnavailable, navigate]);

  return (
    <Container padding={0} className="mb-[92px] h-[110%] min-h-[100vh] w-[100vw] bg-[url('src/assets/altBg.jpg')] bg-cover  bg-repeat-y  ">
      <Header />
      <EmailVerificationBanner />
      <main className=' align-center  mx-auto mt-[30px] w-full max-w-[1320px]   text-center    xl:mt-[62px] '>
        <section className='w-full  '>
          <ClientSearchBarTwo />
          <article className='mt-[43px] flex flex-wrap gap-5 pb-[43px]'>{children}</article>
          <div className='shadow-lg'>
            {/* {isDataUnavailable && <ClientValidateToast showCardToast={showCardToast} />} */}
            {/* <ClientValidateToast showCardToast={showCardToast} /> */}
            {/* {!hasCreditCard && <ClientBankToast onClose={() => setShowBankToast((prev) => !prev)} />} */}
          </div>
        </section>
      </main>

    </Container>
  );
};

export default DashboardLayout;
