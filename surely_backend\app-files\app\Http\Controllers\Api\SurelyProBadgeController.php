<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\SurelyProBadgeResource;
use App\Models\SurelyProBadge;
use Illuminate\Http\Request;

class SurelyProBadgeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     *
     */
    public function index(): \Illuminate\Http\JsonResponse
    {
        $proBadgeData = SurelyProBadge::where('user_id', auth()->id())->get();

        if ($proBadgeData->isEmpty()) {
            return response()->json([
                'error' => true,
                'message' => 'No data found!',
                'data' => [],
            ]);
        }

        return response()->json([
            'error' => false,
            'message' => 'Data retrieved successfully!',
            'data' => SurelyProBadgeResource::collection($proBadgeData),
        ]);
    }

    public function store(Request $request): \Illuminate\Http\JsonResponse
    {
        $data = $request->all();

        // Check if badge already exists for this user
        $proBadgeData = SurelyProBadge::where('text', $data['text'])->where('user_id', auth()->id())->count();

        if ($proBadgeData === 1) {
            return response()->json([
                'error' => true,
                'message' => 'This badge exists! Try to add another one!',
                'data' => [],
            ]);
        }

        // Special validation for Inclusivity Pledge badge
        if ($data['type'] === 'InclusivityPledge') {
            // Check if the score is provided and is 100%
            if (!isset($data['score']) || $data['score'] < 100) {
                return response()->json([
                    'error' => true,
                    'message' => 'Inclusivity Pledge badge can only be awarded after completing the test with a perfect score.',
                    'data' => [],
                ]);
            }
        }

        $request->merge([
            'user_id' => auth()->id()
        ]);

        $data = SurelyProBadge::create($request->all());

        return response()->json([
            'error' => false,
            'message' => 'Data saved successfully!',
            'data' => $data,
        ]);
    }
}
